# ESoonPay 电商企业分账系统服务平台

## 项目简介

ESoonPay是一个专为电商企业打造的分账系统服务平台官网，基于Vue 3 + Element Plus构建。该平台致力于解决电商企业多平台资金管理痛点，提供统一的分账管理解决方案。

## 核心功能

### 🏪 多渠道收单聚合
- 支持抖音、天猫、京东等20+主流电商平台
- 自动归集各平台收单资金到分账系统
- 统一的收单管理界面

### 💰 供应链金融服务
- 基于真实交易数据提供融资服务
- 应收账款融资、库存融资等多种产品
- 快速审批，灵活还款

### 🤖 智能分账系统
- 基于预设规则自动分账
- 支持供应商、员工、合作伙伴批量结算
- 实时状态监控

### 🌍 跨境结汇服务
- 支持多币种收款和结汇
- 实时汇率查询和锁汇服务
- 合规的跨境结汇流程

## 技术栈

- **前端框架**: Vue 3
- **UI组件库**: Element Plus
- **路由管理**: Vue Router 4
- **构建工具**: Vite
- **样式**: CSS3 + 响应式设计

## 项目结构

```
src/
├── components/          # 公共组件
│   ├── Navbar.vue      # 导航栏
│   ├── Footer.vue      # 页脚
│   ├── HeroSection.vue # 英雄区
│   ├── DataCards.vue   # 数据展示卡片
│   ├── PainPointSection.vue # 痛点分析
│   ├── SolutionAdvantages.vue # 解决方案优势
│   ├── CostCalculator.vue # 降本增效测算工具
│   ├── CustomerCases.vue # 客户案例
│   └── CallToAction.vue # CTA区域
├── views/              # 页面组件
│   ├── Home.vue        # 首页
│   ├── Solutions.vue   # 解决方案页
│   ├── Scenarios.vue   # 应用场景页
│   └── About.vue       # 关于我们页
├── router/             # 路由配置
│   └── index.js
├── assets/             # 静态资源
├── App.vue             # 根组件
└── main.js             # 入口文件
```

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 页面功能

### 🏠 首页 (Home)
- 英雄区展示核心价值主张
- 数据卡片展示服务规模
- 痛点分析与解决方案对比
- 降本增效测算工具
- 客户成功案例
- CTA转化区域

### 💡 解决方案页 (Solutions)
- 详细的解决方案介绍
- 技术架构展示
- API接口文档
- 功能特性说明

### 🎯 应用场景页 (Scenarios)
- 多店铺管理场景
- 供应链结算场景
- 跨境贸易场景
- 佣金发放场景
- 流程图展示

### 👥 关于我们页 (About)
- 公司介绍与使命
- 发展历程时间线
- 核心团队介绍
- 合作伙伴展示
- 资质认证
- 联系方式

## 设计特色

### 🎨 视觉设计
- **主色调**: 深蓝色(专业) + 绿色(安全)
- **响应式设计**: 完美适配桌面端和移动端
- **现代化UI**: 使用渐变、阴影、动画等现代设计元素

### 📊 数据可视化
- 动态数字动画
- 进度条展示
- 对比图表
- 流程图

### 🔄 交互体验
- 平滑滚动
- 悬停效果
- 加载动画
- 表单验证

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 项目亮点

1. **完整的业务场景覆盖** - 从痛点分析到解决方案，全面展示虚拟公户价值
2. **专业的金融级设计** - 体现安全性和专业性的视觉设计
3. **丰富的交互体验** - 成本测算工具、动画效果等增强用户体验
4. **响应式设计** - 完美适配各种设备尺寸
5. **模块化架构** - 组件化开发，易于维护和扩展

## 许可证

MIT License
