<template>
  <div class="register-page">
    <div class="register-container">
      <!-- 左侧介绍区域 -->
      <div class="intro-section">
        <div class="intro-content">
          <div class="brand-section">
            <h1 class="brand-title">云账户管理后台</h1>
            <p class="brand-subtitle">专业的企业资金管理解决方案</p>
          </div>

          <div class="feature-image">
            <div class="image-placeholder">
              <i class="el-icon-bank-card" style="font-size: 80px; color: rgba(255, 255, 255, 0.8); margin-bottom: 12px;"></i>
              <h3>分账系统</h3>
              <p>专业的企业资金管理解决方案</p>
            </div>
          </div>

          <div class="process-steps">
            <h3>开通流程</h3>
            <div class="steps-list">
              <div class="step-item">
                <div class="step-number">1</div>
                <div class="step-content">
                  <h4>注册账户</h4>
                  <p>填写基本信息，完成账户注册</p>
                </div>
              </div>
              <div class="step-item">
                <div class="step-number">2</div>
                <div class="step-content">
                  <h4>企业认证</h4>
                  <p>上传企业资质，完成实名认证</p>
                </div>
              </div>
              <div class="step-item">
                <div class="step-number">3</div>
                <div class="step-content">
                  <h4>开通服务</h4>
                  <p>审核通过后，即可开通虚拟公户</p>
                </div>
              </div>
            </div>
          </div>

          <div class="efficiency-stats">
            <div class="stat-item">
              <div class="stat-number">5分钟</div>
              <div class="stat-label">快速注册</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">24小时</div>
              <div class="stat-label">审核时效</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">0费用</div>
              <div class="stat-label">开户成本</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧注册表单 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2>免费开通分账系统</h2>
            <p>已有账户？<router-link to="/login" class="login-link">立即登录</router-link></p>
          </div>

          <el-form 
            :model="registerForm" 
            :rules="registerRules" 
            ref="registerFormRef" 
            label-width="0"
            class="register-form"
          >
            <!-- 注册方式切换 -->
            <div class="register-type-tabs">
              <div 
                class="tab-item" 
                :class="{ active: registerType === 'phone' }"
                @click="registerType = 'phone'"
              >
                手机号注册
              </div>
              <div 
                class="tab-item" 
                :class="{ active: registerType === 'email' }"
                @click="registerType = 'email'"
              >
                邮箱注册
              </div>
            </div>

            <!-- 手机号注册 -->
            <template v-if="registerType === 'phone'">
              <el-form-item prop="phone">
                <el-input
                  v-model="registerForm.phone"
                  placeholder="请输入手机号"
                  size="medium"
                  maxlength="11"
                >
                  <i slot="prefix" class="el-icon-phone"></i>
                </el-input>
              </el-form-item>

              <el-form-item prop="phoneCode">
                <el-input
                  v-model="registerForm.phoneCode"
                  placeholder="请输入验证码"
                  size="medium"
                  maxlength="6"
                >
                  <i slot="prefix" class="el-icon-message"></i>
                  <el-button
                    slot="suffix"
                    class="code-btn-inline"
                    :disabled="phoneCodeDisabled"
                    @click="sendPhoneCode"
                    type="text"
                  >
                    {{ phoneCodeText }}
                  </el-button>
                </el-input>
              </el-form-item>
            </template>

            <!-- 邮箱注册 -->
            <template v-if="registerType === 'email'">
              <el-form-item prop="email">
                <el-input
                  v-model="registerForm.email"
                  placeholder="请输入邮箱地址"
                  size="medium"
                >
                  <i slot="prefix" class="el-icon-message"></i>
                </el-input>
              </el-form-item>

              <el-form-item prop="emailCode">
                <el-input
                  v-model="registerForm.emailCode"
                  placeholder="请输入验证码"
                  size="medium"
                  maxlength="6"
                >
                  <i slot="prefix" class="el-icon-key"></i>
                  <el-button
                    slot="suffix"
                    class="code-btn-inline"
                    :disabled="emailCodeDisabled"
                    @click="sendEmailCode"
                    type="text"
                  >
                    {{ emailCodeText }}
                  </el-button>
                </el-input>
              </el-form-item>
            </template>

            <!-- 登录密码 -->
            <div class="password-field-wrapper" :class="{ 'has-strength-tip': !!registerForm.password }">
              <el-form-item prop="password" class="password-form-item">
                <el-input
                  v-model="registerForm.password"
                  type="password"
                  placeholder="请设置登录密码"
                  size="medium"
                  show-password
                  @input="onPasswordInput"
                >
                  <i slot="prefix" class="el-icon-lock"></i>
                </el-input>
              </el-form-item>
              <!-- 密码安全级别提示 -->
              <div class="password-strength" :class="{ 'is-visible': !!registerForm.password }">
                <div class="strength-bar">
                  <div
                    class="strength-fill"
                    :class="passwordStrength.level || 'default'"
                    :style="{ width: (passwordStrength.percentage || 0) + '%' }"
                  ></div>
                </div>
                <div class="strength-text">
                  <div class="strength-level" :class="passwordStrength.level || 'default'">
                    {{ passwordStrength.text || '安全级别：' }}
                  </div>
                  <div class="strength-tips">
                    {{ passwordStrength.tips || '请输入密码以查看安全级别' }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 邀请码 -->
            <el-form-item prop="inviteCode" v-if="showInviteCodeField">
              <el-input
                v-model="registerForm.inviteCode"
                placeholder="邀请码（选填）"
                size="medium"
              >
                <i slot="prefix" class="el-icon-ticket"></i>
              </el-input>
            </el-form-item>

            <!-- 协议勾选 -->
            <el-form-item prop="agreement" class="agreement-item">
              <el-checkbox v-model="registerForm.agreement">
                我已阅读并同意遵守
                <a href="#" @click.prevent="openUserAgreement" class="agreement-link">《用户服务协议》</a>
                和
                <a href="#" @click.prevent="openPrivacyPolicy" class="agreement-link">《隐私政策》</a>
              </el-checkbox>
            </el-form-item>

            <!-- 注册按钮 -->
            <el-form-item>
              <el-button 
                type="primary" 
                size="medium" 
                class="register-btn"
                :loading="submitting"
                @click="handleRegister"
              >
                立即注册
              </el-button>
            </el-form-item>
          </el-form>

          <div class="form-footer">
            <p>注册即表示您同意我们的服务条款和隐私政策</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Register",
  data() {
    return {
      // 注册类型
      registerType: 'phone',
      
      // 表单数据
      registerForm: {
        phone: '',
        phoneCode: '',
        email: '',
        emailCode: '',
        password: '',
        inviteCode: '',
        agreement: false
      },
      
      // 提交状态
      submitting: false,
      
      // 验证码倒计时
      phoneCountdown: 0,
      emailCountdown: 0,
      
      // 表单验证规则
      registerRules: {
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        phoneCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { len: 6, message: '验证码为6位数字', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        emailCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { len: 6, message: '验证码为6位数字', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请设置登录密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' },
          { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字', trigger: 'blur' }
        ],
        agreement: [
          { 
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请阅读并同意服务协议'))
              } else {
                callback()
              }
            }, 
            trigger: 'change' 
          }
        ]
      }
    }
  },
