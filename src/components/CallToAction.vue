<template>
  <section class="cta-section">
    <div class="cta-background">
      <div class="cta-pattern"></div>
    </div>
    
    <div class="container-wide">
      <div class="cta-content">
        <div class="cta-text">
          <h2 class="cta-title">
            准备好开启
            <span class="highlight">智能资金管理</span>
            之旅了吗？
          </h2>
          <p class="cta-subtitle">
            加入10,000+电商企业的选择，体验分账系统带来的效率革命
          </p>
          
          <div class="cta-features">
            <div class="feature-item" v-for="feature in ctaFeatures" :key="feature.text">
              <el-icon class="feature-icon">
                <component :is="feature.icon" />
              </el-icon>
              <span class="feature-text">{{ feature.text }}</span>
            </div>
          </div>
        </div>
        
        <div class="cta-actions">
          <div class="action-buttons">
            <el-button type="primary" size="large" class="primary-cta" @click="openRegister">
              <el-icon><Star /></el-icon>
              立即免费开通
            </el-button>
            <el-button size="large" class="secondary-cta" @click="contactSales">
              <el-icon><Phone /></el-icon>
              联系专属顾问
            </el-button>
          </div>
          
          <div class="cta-benefits">
            <div class="benefit-item" v-for="benefit in ctaBenefits" :key="benefit">
              <el-icon class="benefit-icon"><Check /></el-icon>
              <span>{{ benefit }}</span>
            </div>
          </div>
          
          <div class="urgency-notice">
            <el-icon class="notice-icon"><Clock /></el-icon>
            <span class="notice-text">限时优惠：前100名用户享受首年5折优惠</span>
          </div>
        </div>
      </div>
      
      <!-- 开通流程 */
      <div class="process-section">
        <h3 class="process-title">极简开通流程</h3>
        <div class="process-steps">
          <div class="step-item" v-for="(step, index) in processSteps" :key="index">
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <div class="step-icon">
                <el-icon :size="32">
                  <component :is="step.icon" />
                </el-icon>
              </div>
              <h4 class="step-title">{{ step.title }}</h4>
              <p class="step-desc">{{ step.description }}</p>
              <div class="step-time">{{ step.time }}</div>
            </div>
            <div class="step-arrow" v-if="index < processSteps.length - 1">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 联系信息 -->
      <div class="contact-section">
        <div class="contact-grid">
          <div class="contact-item" v-for="contact in contactMethods" :key="contact.type">
            <div class="contact-icon">
              <el-icon :size="24">
                <component :is="contact.icon" />
              </el-icon>
            </div>
            <div class="contact-info">
              <div class="contact-label">{{ contact.label }}</div>
              <div class="contact-value">{{ contact.value }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  Star,
  Phone,
  Check,
  Clock,
  ArrowRight,
  Document,
  UserFilled,
  CircleCheckFilled,
  Message,
  Location,
  Service
} from '@element-plus/icons-vue'

const ctaFeatures = [
  { icon: Check, text: '5分钟快速接入' },
  { icon: Check, text: '首月免费试用' },
  { icon: Check, text: '专属客服支持' },
  { icon: Check, text: '银行级安全保障' }
]

const ctaBenefits = [
  '无需预付费用，按实际使用量计费',
  '7×24小时技术支持服务',
  '30天无理由退款保障',
  '免费提供API技术对接支持'
]

const processSteps = [
  {
    icon: Document,
    title: '提交资料',
    description: '上传营业执照等基础资料',
    time: '2分钟'
  },
  {
    icon: UserFilled,
    title: '身份验证',
    description: '法人人脸识别验证',
    time: '3分钟'
  },
  {
    icon: Service,
    title: '系统配置',
    description: '自动配置虚拟账户',
    time: '30分钟'
  },
  {
    icon: CircleCheckFilled,
    title: '开通完成',
    description: '开始使用分账系统服务',
    time: '立即使用'
  }
]

import { getAllContactMethods } from '../config/contact.js'

const contactMethods = getAllContactMethods().map(contact => ({
  type: contact.type,
  icon: contact.type === 'phone' ? Phone :
        contact.type === 'email' ? Message :
        contact.type === 'address' ? Location : Service,
  label: contact.label,
  value: contact.value
}))

const openRegister = () => {
  try {
    // 动态导入设备检测工具
    import('../utils/deviceDetect.js').then(({ redirectToRegister }) => {
      const registerUrl = redirectToRegister()
      window.open(registerUrl, '_blank')
    })
  } catch (error) {
    console.error('跳转注册页面失败:', error)
  }
}

const contactSales = () => {
  console.log('联系销售')
}
</script>

<style scoped>
.cta-section {
  position: relative;
  padding: 100px 0;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #10b981 100%);
  color: white;
  overflow: hidden;
}

.cta-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.cta-pattern {
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 20%, white 2px, transparent 2px),
    radial-gradient(circle at 80% 80%, white 2px, transparent 2px);
  background-size: 80px 80px;
  background-position: 0 0, 40px 40px;
}

.cta-content {
  display: grid;
  grid-template-columns: 1.3fr 1fr;
  gap: 6rem;
  align-items: center;
  margin-bottom: 5rem;
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 5rem;
}

.cta-title {
  font-size: 3rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.highlight {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cta-subtitle {
  font-size: 1.3rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
}

.cta-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.feature-icon {
  color: #10b981;
  font-size: 1.2rem;
}

.feature-text {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.cta-actions {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.primary-cta {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border: none;
  padding: 18px 36px;
  font-size: 1.2rem;
  font-weight: 700;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(251, 191, 36, 0.3);
}

.primary-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(251, 191, 36, 0.4);
}

.secondary-cta {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 16px 34px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.secondary-cta:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.cta-benefits {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
}

.benefit-icon {
  color: #10b981;
  font-size: 1rem;
}

.urgency-notice {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  background: rgba(251, 191, 36, 0.2);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.notice-icon {
  color: #fbbf24;
  font-size: 1.2rem;
}

.notice-text {
  font-weight: 600;
  color: #fbbf24;
}

.process-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  margin-bottom: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.process-title {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 3rem;
  color: white;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  align-items: center;
}

.step-item {
  position: relative;
  text-align: center;
}

.step-number {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background: #fbbf24;
  color: #1e3a8a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.9rem;
  z-index: 2;
}

.step-content {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem 1.5rem 1.5rem;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  margin: 0 auto 1rem;
  color: white;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: white;
}

.step-desc {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  line-height: 1.4;
}

.step-time {
  background: #10b981;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-block;
}

.step-arrow {
  position: absolute;
  right: -1rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.5rem;
}

.contact-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
}

.contact-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.3rem;
}

.contact-value {
  font-weight: 600;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cta-section {
    padding: 80px 0;
  }

  .cta-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .cta-title {
    font-size: 2.5rem;
  }

  .cta-features {
    grid-template-columns: 1fr;
  }

  .process-steps {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .step-arrow {
    display: none;
  }

  .contact-grid {
    grid-template-columns: 1fr;
  }
}
</style>
