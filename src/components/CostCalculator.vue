<template>
  <section class="calculator-section">
    <div class="container-wide">
      <div class="section-header">
        <h2 class="section-title">降本增效测算</h2>
        <p class="section-subtitle">
          输入您的月流水，立即计算使用分账系统能为您带来多少降本增效价值
        </p>
      </div>
      
      <div class="calculator-container">
        <div class="calculator-form">
          <div class="form-group">
            <label class="form-label">月流水金额（万元）</label>
            <el-input-number
              v-model="monthlyRevenue"
              :min="1"
              :max="10000"
              :step="10"
              size="large"
              class="revenue-input"
              @change="calculateSavings"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">平台数量</label>
            <el-select v-model="platformCount" size="large" @change="calculateSavings">
              <el-option label="1-2个平台" :value="2" />
              <el-option label="3-5个平台" :value="4" />
              <el-option label="6-10个平台" :value="8" />
              <el-option label="10个以上平台" :value="15" />
            </el-select>
          </div>
          
          <div class="form-group">
            <label class="form-label">业务类型</label>
            <el-select v-model="businessType" size="large" @change="calculateSavings">
              <el-option label="服饰鞋包" value="fashion" />
              <el-option label="3C数码" value="electronics" />
              <el-option label="美妆个护" value="beauty" />
              <el-option label="食品生鲜" value="food" />
              <el-option label="跨境电商" value="crossborder" />
              <el-option label="其他" value="other" />
            </el-select>
          </div>
        </div>
        
        <div class="calculator-results">
          <div class="results-header">
            <h3>预计降本增效价值</h3>
            <div class="savings-amount">
              <span class="currency">¥</span>
              <span class="amount">{{ formatNumber(totalSavings) }}</span>
              <span class="period">/年</span>
            </div>
          </div>
          
          <div class="savings-breakdown">
            <div class="breakdown-item" v-for="item in savingsBreakdown" :key="item.type">
              <div class="breakdown-header">
                <el-icon class="breakdown-icon">
                  <component :is="item.icon" />
                </el-icon>
                <span class="breakdown-title">{{ item.title }}</span>
              </div>
              <div class="breakdown-comparison">
                <div class="cost-item traditional">
                  <span class="cost-label">传统方式</span>
                  <span class="cost-value">¥{{ formatNumber(item.traditional) }}</span>
                </div>
                <div class="cost-item virtual">
                  <span class="cost-label">分账系统</span>
                  <span class="cost-value">¥{{ formatNumber(item.virtual) }}</span>
                </div>
                <div class="cost-savings">
                  <span class="savings-label">降本增效</span>
                  <span class="savings-value">¥{{ formatNumber(item.savings) }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="efficiency-metrics">
            <h4>降本增效指标</h4>
            <div class="metrics-grid">
              <div class="metric-item">
                <div class="metric-value">{{ efficiencyMetrics.timeReduction }}%</div>
                <div class="metric-label">时间效率提升</div>
              </div>
              <div class="metric-item">
                <div class="metric-value">{{ efficiencyMetrics.errorReduction }}%</div>
                <div class="metric-label">错误率降低</div>
              </div>
              <div class="metric-item">
                <div class="metric-value">{{ efficiencyMetrics.automationLevel }}%</div>
                <div class="metric-label">自动化程度</div>
              </div>
            </div>
          </div>
          
          <div class="cta-section">
            <el-button type="primary" size="large" class="cta-button" @click="startTrial">
              <el-icon><Star /></el-icon>
              立即开通，享受优惠
            </el-button>
            <p class="cta-note">首月免费试用，无风险体验</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed } from 'vue'
import { 
  Money, 
  Timer, 
  Document, 
  TrendCharts,
  Star
} from '@element-plus/icons-vue'

const monthlyRevenue = ref(100) // 月流水（万元）
const platformCount = ref(4) // 平台数量
const businessType = ref('fashion') // 业务类型

// 计算节省成本
const calculateSavings = () => {
  // 这里可以添加更复杂的计算逻辑
}

// 成本分解
const savingsBreakdown = computed(() => {
  const revenue = monthlyRevenue.value * 10000 // 转换为元
  const platforms = platformCount.value

  // 根据流水规模动态计算账户管理费
  const getAccountManagementFee = (monthlyRevenue, platforms) => {
    const yearlyRevenue = monthlyRevenue * 12 * 10000 // 年流水（元）

    // 传统方式：基础平台费 + 流水相关费用
    const basePlatformFee = platforms * 200 * 12 // 每个平台每月200元基础费
    const revenueBasedFee = yearlyRevenue * 0.0008 // 年流水的0.08%
    const traditional = basePlatformFee + revenueBasedFee

    // 虚拟公户：固定年费 + 少量流水费用
    const virtualBaseFee = 1200 // 基础年费
    const virtualRevenueFee = yearlyRevenue * 0.0002 // 年流水的0.02%
    const virtual = virtualBaseFee + virtualRevenueFee

    return {
      traditional: Math.round(traditional),
      virtual: Math.round(virtual),
      savings: Math.round(traditional - virtual)
    }
  }

  const accountFees = getAccountManagementFee(monthlyRevenue.value, platforms)

  return [
    {
      type: 'account',
      title: '账户管理费',
      icon: Money,
      traditional: accountFees.traditional,
      virtual: accountFees.virtual,
      savings: accountFees.savings
    },
    {
      type: 'operation',
      title: '人工操作成本',
      icon: Timer,
      traditional: Math.round(revenue * 0.002 * 12), // 年流水的0.2%
      virtual: Math.round(revenue * 0.0005 * 12), // 年流水的0.05%
      savings: Math.round(revenue * 0.0015 * 12)
    },
    {
      type: 'compliance',
      title: '合规处理费用',
      icon: Document,
      traditional: Math.round(revenue * 0.001 * 12), // 年流水的0.1%
      virtual: 0, // 免费
      savings: Math.round(revenue * 0.001 * 12)
    },
    {
      type: 'efficiency',
      title: '效率提升收益',
      icon: TrendCharts,
      traditional: 0,
      virtual: Math.round(revenue * 0.005 * 12), // 年流水的0.5%收益
      savings: Math.round(revenue * 0.005 * 12)
    }
  ]
})

// 总节省金额
const totalSavings = computed(() => {
  return savingsBreakdown.value.reduce((total, item) => total + item.savings, 0)
})

// 效率指标
const efficiencyMetrics = computed(() => {
  const baseReduction = 60
  const platformBonus = Math.min(platformCount.value * 5, 30)
  
  return {
    timeReduction: baseReduction + platformBonus,
    errorReduction: 85,
    automationLevel: 95
  }
})

// 格式化数字
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toLocaleString()
}

const startTrial = () => {
  try {
    // 动态导入设备检测工具
    import('../utils/deviceDetect.js').then(({ redirectToRegister }) => {
      const registerUrl = redirectToRegister()
      window.open(registerUrl, '_blank')
    })
  } catch (error) {
    console.error('跳转注册页面失败:', error)
  }
}
</script>

<style scoped>
.calculator-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  color: white;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  color: white;
}

.section-subtitle {
  color: rgba(255, 255, 255, 0.9);
}

.calculator-container {
  display: grid;
  grid-template-columns: 1fr 1.8fr;
  gap: 5rem;
  max-width: 1400px;
  margin: 0 auto;
}

.calculator-form {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-group {
  margin-bottom: 2rem;
}

.form-label {
  display: block;
  margin-bottom: 0.8rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.revenue-input {
  width: 100%;
}

.calculator-results {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  color: var(--text-color);
}

.results-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.results-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.savings-amount {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.5rem;
}

.currency {
  font-size: 1.5rem;
  color: var(--text-light);
}

.amount {
  font-size: 3rem;
  font-weight: 800;
  color: var(--secondary-color);
}

.period {
  font-size: 1.2rem;
  color: var(--text-light);
}

.savings-breakdown {
  margin-bottom: 2rem;
}

.breakdown-item {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.breakdown-header {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.breakdown-icon {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.breakdown-title {
  font-weight: 600;
  color: var(--text-color);
}

.breakdown-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
}

.cost-item {
  text-align: center;
  padding: 1rem;
  border-radius: 8px;
}

.cost-item.traditional {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.cost-item.virtual {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.cost-savings {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  text-align: center;
  padding: 1rem;
  border-radius: 8px;
}

.cost-label,
.savings-label {
  display: block;
  font-size: 0.8rem;
  color: var(--text-light);
  margin-bottom: 0.5rem;
}

.cost-value,
.savings-value {
  font-weight: 700;
  font-size: 1.1rem;
}

.traditional .cost-value {
  color: #dc2626;
}

.virtual .cost-value {
  color: var(--primary-color);
}

.savings-value {
  color: var(--secondary-color);
}

.efficiency-metrics {
  margin-bottom: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.efficiency-metrics h4 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.metric-item {
  text-align: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 12px;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.metric-label {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-top: 0.5rem;
}

.cta-section {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.cta-button {
  background: linear-gradient(135deg, var(--secondary-color), #059669);
  border: none;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  margin-bottom: 1rem;
}

.cta-note {
  color: var(--text-light);
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .calculator-section {
    padding: 80px 0;
  }

  .calculator-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .breakdown-comparison {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .savings-amount {
    flex-direction: column;
    gap: 0;
  }

  .amount {
    font-size: 2.5rem;
  }
}
</style>
