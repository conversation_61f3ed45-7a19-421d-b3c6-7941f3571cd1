<template>
  <section class="settlement-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">跨境结汇</h2>
        <p class="section-subtitle">
          专业的跨境电商结汇服务，支持多币种收款和结汇，助力企业全球化发展
        </p>
      </div>
      
      <div class="content-layout">
        <!-- 左侧服务介绍 -->
        <div class="service-intro">
          <div class="intro-card">
            <div class="card-header">
              <div class="service-icon">
                <el-icon :size="60">
                  <Globe />
                </el-icon>
              </div>
              <div class="service-info">
                <h3 class="service-title">跨境结汇服务</h3>
                <p class="service-desc">安全、便捷、合规的跨境资金结算解决方案</p>
              </div>
            </div>
            
            <div class="service-features">
              <div class="feature-item" v-for="feature in serviceFeatures" :key="feature.title">
                <div class="feature-icon">
                  <el-icon :size="20">
                    <component :is="feature.icon" />
                  </el-icon>
                </div>
                <div class="feature-content">
                  <h4 class="feature-title">{{ feature.title }}</h4>
                  <p class="feature-desc">{{ feature.description }}</p>
                </div>
              </div>
            </div>
            
            <div class="currency-support">
              <h4 class="currency-title">支持币种</h4>
              <div class="currency-grid">
                <div class="currency-item" v-for="currency in supportedCurrencies" :key="currency.code">
                  <span class="currency-code">{{ currency.code }}</span>
                  <span class="currency-name">{{ currency.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧优势展示 -->
        <div class="advantages-showcase">
          <div class="advantage-cards">
            <div class="advantage-card" v-for="advantage in advantages" :key="advantage.title">
              <div class="advantage-icon">
                <el-icon :size="32">
                  <component :is="advantage.icon" />
                </el-icon>
              </div>
              <h4 class="advantage-title">{{ advantage.title }}</h4>
              <p class="advantage-desc">{{ advantage.description }}</p>
              <div class="advantage-highlights">
                <div class="highlight" v-for="highlight in advantage.highlights" :key="highlight">
                  <el-icon class="highlight-icon"><Check /></el-icon>
                  <span>{{ highlight }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 结汇流程 -->
      <div class="process-section">
        <h3 class="process-title">结汇流程</h3>
        <div class="process-flow">
          <div class="flow-step" v-for="(step, index) in settlementProcess" :key="step.title">
            <div class="step-indicator">
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-icon">
                <el-icon :size="24">
                  <component :is="step.icon" />
                </el-icon>
              </div>
            </div>
            <div class="step-content">
              <h4 class="step-title">{{ step.title }}</h4>
              <p class="step-desc">{{ step.description }}</p>
              <div class="step-time">{{ step.timeframe }}</div>
            </div>
            <div v-if="index < settlementProcess.length - 1" class="step-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 核心数据 -->
      <div class="metrics-section">
        <div class="metrics-grid">
          <div class="metric-card" v-for="metric in keyMetrics" :key="metric.label">
            <div class="metric-icon">
              <el-icon :size="28">
                <component :is="metric.icon" />
              </el-icon>
            </div>
            <div class="metric-value">{{ metric.value }}</div>
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-desc">{{ metric.description }}</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  Globe,
  Check,
  ArrowRight,
  Timer,
  Shield,
  TrendCharts,
  Connection,
  Wallet,
  DocumentCopy,
  DataAnalysis
} from '@element-plus/icons-vue'

// 服务特性
const serviceFeatures = [
  {
    icon: Globe,
    title: '全球收款',
    description: '支持200+国家和地区的跨境收款服务'
  },
  {
    icon: Timer,
    title: '快速结汇',
    description: 'T+1结汇到账，提升资金周转效率'
  },
  {
    icon: Shield,
    title: '合规安全',
    description: '符合外汇管理规定，资金安全有保障'
  },
  {
    icon: TrendCharts,
    title: '优惠汇率',
    description: '提供市场化汇率，降低汇兑成本'
  }
]

// 支持币种
const supportedCurrencies = [
  { code: 'USD', name: '美元' },
  { code: 'EUR', name: '欧元' },
  { code: 'GBP', name: '英镑' },
  { code: 'JPY', name: '日元' },
  { code: 'AUD', name: '澳元' },
  { code: 'CAD', name: '加元' },
  { code: 'HKD', name: '港币' },
  { code: 'SGD', name: '新币' }
]

// 核心优势
const advantages = [
  {
    icon: Connection,
    title: '多渠道接入',
    description: '支持多种跨境收款方式',
    highlights: [
      '银行电汇',
      '第三方支付',
      '数字钱包',
      'B2B平台'
    ]
  },
  {
    icon: DataAnalysis,
    title: '智能风控',
    description: '多维度风险识别和控制',
    highlights: [
      '实时监控',
      '异常预警',
      '合规检查',
      '风险评估'
    ]
  }
]

// 结汇流程
const settlementProcess = [
  {
    icon: DocumentCopy,
    title: '收款确认',
    description: '确认外币资金到账',
    timeframe: '实时'
  },
  {
    icon: TrendCharts,
    title: '汇率锁定',
    description: '选择结汇汇率并锁定',
    timeframe: '即时'
  },
  {
    icon: Shield,
    title: '合规审核',
    description: '进行合规性审核检查',
    timeframe: '2小时'
  },
  {
    icon: Wallet,
    title: '资金结汇',
    description: '完成结汇并转入人民币',
    timeframe: 'T+1'
  }
]

// 关键指标
const keyMetrics = [
  {
    icon: Globe,
    value: '200+',
    label: '覆盖国家',
    description: '全球收款网络覆盖'
  },
  {
    icon: Timer,
    value: 'T+1',
    label: '结汇时效',
    description: '快速资金到账'
  },
  {
    icon: TrendCharts,
    value: '优惠汇率',
    label: '汇率优势',
    description: '降低汇兑成本'
  },
  {
    icon: Shield,
    value: '100%',
    label: '合规保障',
    description: '符合监管要求'
  }
]
</script>

<style scoped>
.settlement-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #0369a1;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--text-light);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.content-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 5rem;
}

.intro-card {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(3, 105, 161, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2.5rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.service-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #0369a1, #0284c7);
  border-radius: 16px;
  color: white;
}

.service-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.service-desc {
  color: var(--text-light);
  font-size: 1rem;
  line-height: 1.5;
}

.service-features {
  margin-bottom: 2.5rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(3, 105, 161, 0.1);
  border-radius: 8px;
  color: #0369a1;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.feature-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.feature-desc {
  color: var(--text-light);
  line-height: 1.5;
  font-size: 0.95rem;
}

.currency-support {
  padding: 2rem;
  background: rgba(3, 105, 161, 0.05);
  border-radius: 16px;
}

.currency-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  text-align: center;
}

.currency-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.currency-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid rgba(3, 105, 161, 0.1);
}

.currency-code {
  font-size: 1rem;
  font-weight: 700;
  color: #0369a1;
  margin-bottom: 0.3rem;
}

.currency-name {
  font-size: 0.8rem;
  color: var(--text-light);
}

.advantage-cards {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.advantage-card {
  background: white;
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(3, 105, 161, 0.1);
  transition: all 0.3s ease;
}

.advantage-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(3, 105, 161, 0.12);
}

.advantage-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #0369a1, #0284c7);
  border-radius: 12px;
  color: white;
  margin-bottom: 1.5rem;
}

.advantage-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.advantage-desc {
  color: var(--text-light);
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.advantage-highlights {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.highlight {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 0.9rem;
  color: var(--text-color);
}

.highlight-icon {
  color: #22c55e;
  font-size: 1rem;
}

.process-section {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  margin-bottom: 3rem;
}

.process-title {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 3rem;
}

.process-flow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
  position: relative;
}

.step-indicator {
  position: relative;
  margin-bottom: 1.5rem;
}

.step-number {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: #0369a1;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 700;
  z-index: 2;
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #0369a1, #0284c7);
  border-radius: 50%;
  color: white;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.step-desc {
  color: var(--text-light);
  line-height: 1.4;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.step-time {
  color: #0369a1;
  font-size: 0.8rem;
  font-weight: 600;
}

.step-arrow {
  position: absolute;
  right: -1.5rem;
  top: 30px;
  color: #0369a1;
  font-size: 1.2rem;
}

.metrics-section {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.metric-card {
  text-align: center;
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid rgba(3, 105, 161, 0.1);
  background: rgba(3, 105, 161, 0.02);
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(3, 105, 161, 0.1);
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #0369a1, #0284c7);
  border-radius: 12px;
  color: white;
  margin: 0 auto 1rem;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #0369a1;
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.metric-desc {
  font-size: 0.9rem;
  color: var(--text-light);
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .currency-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .process-flow {
    flex-direction: column;
    gap: 3rem;
  }
  
  .step-arrow {
    display: none;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .settlement-section {
    padding: 80px 0;
  }
  
  .intro-card,
  .process-section,
  .metrics-section {
    padding: 2rem;
  }
  
  .card-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
</style>
