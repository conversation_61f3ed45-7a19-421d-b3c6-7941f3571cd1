<template>
  <section class="cases-section">
    <div class="container-wide">
      <div class="section-header">
        <h2 class="section-title">客户成功案例</h2>
        <p class="section-subtitle">
          看看其他电商企业如何通过虚拟公户实现降本增效
        </p>
      </div>
      
      <div class="cases-grid">
        <div class="case-card" v-for="(caseItem, index) in customerCases" :key="index">
          <div class="case-header">
            <div class="company-info">
              <div class="company-logo">
                <img :src="caseItem.logo" :alt="caseItem.company" />
              </div>
              <div class="company-details">
                <h3 class="company-name">{{ caseItem.company }}</h3>
                <div class="company-meta">
                  <span class="industry">{{ caseItem.industry }}</span>
                  <span class="scale">{{ caseItem.scale }}</span>
                </div>
              </div>
            </div>
            <div class="case-badge">
              <el-icon><Star /></el-icon>
              <span>成功案例</span>
            </div>
          </div>
          
          <div class="case-challenge">
            <h4 class="challenge-title">面临挑战</h4>
            <p class="challenge-desc">{{ caseItem.challenge }}</p>
          </div>
          
          <div class="case-solution">
            <h4 class="solution-title">解决方案</h4>
            <div class="solution-features">
              <div class="feature-tag" v-for="feature in caseItem.solution" :key="feature">
                {{ feature }}
              </div>
            </div>
          </div>
          
          <div class="case-results">
            <h4 class="results-title">实施效果</h4>
            <div class="results-metrics">
              <div class="metric-item" v-for="metric in caseItem.results" :key="metric.label">
                <div class="metric-value" :class="metric.type">{{ metric.value }}</div>
                <div class="metric-label">{{ metric.label }}</div>
              </div>
            </div>
          </div>
          
          <div class="case-quote">
            <el-icon class="quote-icon"><ChatRound /></el-icon>
            <p class="quote-text">{{ caseItem.quote }}</p>
            <div class="quote-author">
              <span class="author-name">{{ caseItem.author.name }}</span>
              <span class="author-title">{{ caseItem.author.title }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 行业分布 -->
      <div class="industry-stats">
        <h3 class="stats-title">服务行业分布</h3>
        <div class="industry-grid">
          <div class="industry-item" v-for="industry in industryStats" :key="industry.name">
            <div class="industry-icon">
              <el-icon :size="32">
                <component :is="industry.icon" />
              </el-icon>
            </div>
            <div class="industry-info">
              <div class="industry-name">{{ industry.name }}</div>
              <div class="industry-percentage">{{ industry.percentage }}%</div>
            </div>
            <div class="industry-bar">
              <div class="bar-fill" :style="{ width: industry.percentage + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  Star,
  ChatRound,
  ShoppingBag,
  Monitor,
  User,
  Coffee,
  Ship
} from '@element-plus/icons-vue'

const customerCases = [
  {
    company: '美丽时尚',
    industry: '服饰鞋包',
    scale: '年GMV 5亿+',
    logo: '/cases/company1.png',
    challenge: '在抖音、快手、天猫等8个平台开店，资金分散管理困难，每月需要人工对账200+小时，经常出现资金周转不及时的问题。',
    solution: ['多平台资金聚合', '智能分账系统', 'T+0实时提现', '自动对账'],
    results: [
      { label: '对账时间减少', value: '90%', type: 'positive' },
      { label: '资金周转效率', value: '+180%', type: 'positive' },
      { label: '年节省成本', value: '68万', type: 'money' },
      { label: '错误率降低', value: '95%', type: 'positive' }
    ],
    quote: '使用分账系统后，我们的财务团队从繁重的对账工作中解放出来，可以专注于更有价值的财务分析工作。资金管理效率提升了3倍以上。',
    author: {
      name: '张总',
      title: '美丽时尚 CEO'
    }
  },
  {
    company: '科技数码城',
    industry: '3C数码',
    scale: '年GMV 8亿+',
    logo: '/cases/company2.png',
    challenge: '跨境电商业务涉及多币种结算，传统银行手续费高，汇率损失大，供应商结算周期长，影响供应链稳定性。',
    solution: ['多币种支持', '智能汇率优化', '供应商快速结算', '风控系统'],
    results: [
      { label: '汇率成本节省', value: '45%', type: 'positive' },
      { label: '结算周期缩短', value: '70%', type: 'positive' },
      { label: '年节省成本', value: '120万', type: 'money' },
      { label: '供应商满意度', value: '+85%', type: 'positive' }
    ],
    quote: '虚拟公户的多币种结算功能帮我们大大降低了跨境贸易成本，供应商结算从原来的7天缩短到当天到账，供应链关系更加稳固。',
    author: {
      name: '李总',
      title: '科技数码城 CFO'
    }
  },
  {
    company: '优选美妆',
    industry: '美妆个护',
    scale: '年GMV 3亿+',
    logo: '/cases/company3.png',
    challenge: '直播带货业务快速增长，达人分佣结算复杂，传统方式需要大量人工处理，容易出错，影响达人合作关系。',
    solution: ['智能分佣系统', '实时结算', '数据透明化', '合规管理'],
    results: [
      { label: '分佣处理效率', value: '+300%', type: 'positive' },
      { label: '结算准确率', value: '99.9%', type: 'positive' },
      { label: '年节省成本', value: '35万', type: 'money' },
      { label: '达人满意度', value: '+92%', type: 'positive' }
    ],
    quote: '智能分佣系统让我们的达人结算变得非常简单，达人们都很满意实时到账的体验，合作关系更加紧密了。',
    author: {
      name: '王总',
      title: '优选美妆 运营总监'
    }
  }
]

const industryStats = [
  {
    name: '服饰鞋包',
    percentage: 28,
    icon: ShoppingBag
  },
  {
    name: '3C数码',
    percentage: 22,
    icon: Monitor
  },
  {
    name: '美妆个护',
    percentage: 18,
    icon: User
  },
  {
    name: '食品生鲜',
    percentage: 15,
    icon: Coffee
  },
  {
    name: '跨境电商',
    percentage: 17,
    icon: Ship
  }
]
</script>

<style scoped>
.cases-section {
  padding: 100px 0;
  background: white;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2.5rem;
  margin-bottom: 5rem;
}

@media (max-width: 1400px) {
  .cases-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 900px) {
  .cases-grid {
    grid-template-columns: 1fr;
  }
}

.case-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.case-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.case-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.company-info {
  display: flex;
  gap: 1rem;
}

.company-logo {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  background: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
}

.company-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.company-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.company-meta {
  display: flex;
  gap: 1rem;
}

.industry,
.scale {
  font-size: 0.9rem;
  color: var(--text-light);
  background: #f1f5f9;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
}

.case-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.case-challenge,
.case-solution,
.case-results {
  margin-bottom: 2rem;
}

.challenge-title,
.solution-title,
.results-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.challenge-desc {
  color: var(--text-light);
  line-height: 1.6;
}

.solution-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.feature-tag {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.results-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.metric-item {
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.3rem;
}

.metric-value.positive {
  color: var(--secondary-color);
}

.metric-value.money {
  color: #f59e0b;
}

.metric-label {
  font-size: 0.8rem;
  color: var(--text-light);
}

.case-quote {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  padding: 1.5rem;
  border-radius: 12px;
  border-left: 4px solid var(--primary-color);
  position: relative;
}

.quote-icon {
  position: absolute;
  top: -10px;
  left: 1rem;
  background: white;
  color: var(--primary-color);
  font-size: 1.5rem;
  padding: 0.5rem;
  border-radius: 50%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.quote-text {
  font-style: italic;
  line-height: 1.6;
  color: var(--text-color);
  margin-bottom: 1rem;
  margin-top: 1rem;
}

.quote-author {
  text-align: right;
}

.author-name {
  font-weight: 600;
  color: var(--text-color);
}

.author-title {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-left: 0.5rem;
}

.industry-stats {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 20px;
  padding: 3rem;
}

.stats-title {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 3rem;
}

.industry-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.industry-item {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.industry-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.industry-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 12px;
  color: white;
  margin-bottom: 1.5rem;
}

.industry-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.industry-name {
  font-weight: 600;
  color: var(--text-color);
}

.industry-percentage {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.industry-bar {
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 4px;
  transition: width 1s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cases-section {
    padding: 80px 0;
  }

  .cases-grid {
    grid-template-columns: 1fr;
  }

  .case-header {
    flex-direction: column;
    gap: 1rem;
  }

  .results-metrics {
    grid-template-columns: 1fr;
  }

  .industry-grid {
    grid-template-columns: 1fr;
  }
}
</style>
