<template>
  <footer class="footer">
    <div class="container-wide">
      <div class="footer-content">
        <!-- 公司信息 -->
        <div class="footer-section">
          <div class="footer-brand">
            <h3>ESoonPay</h3>
            <p class="brand-desc">专业的电商企业分账系统服务平台</p>
          </div>
          <div class="contact-info">
            <p><el-icon><Phone /></el-icon> {{ CONTACT_INFO.phone }}</p>
            <p><el-icon><Message /></el-icon> {{ CONTACT_INFO.emails.service }}</p>
            <p><el-icon><Location /></el-icon> {{ CONTACT_INFO.address.short }}</p>
          </div>
        </div>

        <!-- 产品服务 -->
        <div class="footer-section">
          <h4>产品服务</h4>
          <ul class="footer-links">
            <li><router-link to="/solutions">分账系统</router-link></li>
            <li><router-link to="/scenarios">多平台聚合</router-link></li>
            <li><a href="#" @click.prevent>智能分账</a></li>
            <li><a href="#" @click.prevent>供应链金融</a></li>
          </ul>
        </div>

        <!-- 解决方案 -->
        <div class="footer-section">
          <h4>解决方案</h4>
          <ul class="footer-links">
            <li><a href="#" @click.prevent>多店铺管理</a></li>
            <li><a href="#" @click.prevent>供应链结算</a></li>
            <li><a href="#" @click.prevent>跨境贸易</a></li>
            <li><a href="#" @click.prevent>佣金发放</a></li>
          </ul>
        </div>

        <!-- 帮助支持 -->
        <div class="footer-section">
          <h4>帮助支持</h4>
          <ul class="footer-links">
            <li><a href="#" @click.prevent>开发文档</a></li>
            <li><a href="#" @click.prevent>API接口</a></li>
            <li><a href="#" @click.prevent>常见问题</a></li>
            <li><a href="#" @click.prevent>联系客服</a></li>
          </ul>
        </div>
      </div>

      <!-- 合规声明 -->
      <div class="compliance-notice">
        <div class="compliance-content">
          <el-icon class="shield-icon"><Lock /></el-icon>
          <p>
            <strong>资金安全保障：</strong>
            资金存管于合作银行实体账户，受央行监管，平台不触碰资金。
            合作银行：百信银行、苏商银行、蓝海银行、新网银行等持牌金融机构。
          </p>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="footer-bottom">
        <div class="copyright">
          <p>&copy; 2024 ESoonPay. 保留所有权利.</p>
          <div class="legal-links">
            <router-link to="/privacy-policy">隐私政策</router-link>
            <router-link to="/terms-of-service">服务条款</router-link>
            <router-link to="/legal-notice">法律声明</router-link>
          </div>
        </div>
        
        <!-- 认证标识 -->
        <div class="certifications">
          <div class="cert-item">
            <span class="cert-text">央行监管</span>
          </div>
          <div class="cert-item">
            <span class="cert-text">银行级安全</span>
          </div>
          <div class="cert-item">
            <span class="cert-text">ISO27001认证</span>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { Phone, Message, Location, Lock } from '@element-plus/icons-vue'
import { CONTACT_INFO } from '../config/contact.js'
</script>

<style scoped>
.footer {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  color: white;
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 3rem;
  padding: 4rem 0 2rem;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 1.5rem;
  color: white;
}

.footer-brand h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.brand-desc {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.contact-info p {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 0.8rem;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: white;
}

.compliance-notice {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 2rem 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.compliance-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.shield-icon {
  font-size: 1.5rem;
  color: var(--secondary-color);
  margin-top: 0.2rem;
}

.compliance-content p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 0;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.copyright {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.legal-links {
  display: flex;
  gap: 1.5rem;
}

.legal-links a,
.legal-links .router-link {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.legal-links a:hover,
.legal-links .router-link:hover {
  color: white;
}

.certifications {
  display: flex;
  gap: 1rem;
}

.cert-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.cert-text {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 3rem 0 2rem;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .copyright {
    flex-direction: column;
    gap: 1rem;
  }

  .certifications {
    flex-wrap: wrap;
    justify-content: center;
  }

  .compliance-content {
    flex-direction: column;
    text-align: center;
  }
}
</style>
