<template>
  <section class="hero">
    <div class="hero-background">
      <div class="hero-pattern"></div>
    </div>
    
    <div class="container-wide">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            让电商资金管理
            <span class="highlight">简单如水电</span>
          </h1>
          <p class="hero-subtitle">
            一键聚合20+平台收入，智能分账，降本增效<br>
            专为电商企业打造的分账系统解决方案
          </p>
          
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number">10,000+</div>
              <div class="stat-label">服务企业</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-number">2亿元</div>
              <div class="stat-label">日均处理资金</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-number">T+0</div>
              <div class="stat-label">实时到账</div>
            </div>
          </div>
          
          <div class="hero-actions">
            <el-button type="primary" size="large" class="cta-primary" @click="openRegister">
              <el-icon><Star /></el-icon>
              免费开通测算
            </el-button>
            <el-button size="large" class="cta-secondary" @click="watchDemo">
              <el-icon><VideoCamera /></el-icon>
              观看演示
            </el-button>
          </div>
          
          <div class="trust-indicators">
            <div class="trust-item">
              <el-icon class="trust-icon"><Lock /></el-icon>
              <span>央行监管</span>
            </div>
            <div class="trust-item">
              <el-icon class="trust-icon"><Lock /></el-icon>
              <span>银行级安全</span>
            </div>
            <div class="trust-item">
              <el-icon class="trust-icon"><Check /></el-icon>
              <span>0代码接入</span>
            </div>
          </div>
        </div>
        
        <div class="hero-visual">
          <div class="dashboard-mockup">
            <div class="mockup-header">
              <div class="mockup-controls">
                <span class="control red"></span>
                <span class="control yellow"></span>
                <span class="control green"></span>
              </div>
              <div class="mockup-title">ESoonPay 分账管理台</div>
            </div>
            <div class="mockup-content">
              <div class="platform-cards">
                <div class="platform-card">
                  <div class="platform-icon douyin"></div>
                  <div class="platform-info">
                    <div class="platform-name">抖音小店</div>
                    <div class="platform-amount">¥128,456</div>
                  </div>
                  <div class="status-indicator active"></div>
                </div>
                <div class="platform-card">
                  <div class="platform-icon tmall"></div>
                  <div class="platform-info">
                    <div class="platform-name">天猫店铺</div>
                    <div class="platform-amount">¥89,234</div>
                  </div>
                  <div class="status-indicator active"></div>
                </div>
                <div class="platform-card">
                  <div class="platform-icon jd"></div>
                  <div class="platform-info">
                    <div class="platform-name">京东商城</div>
                    <div class="platform-amount">¥67,890</div>
                  </div>
                  <div class="status-indicator active"></div>
                </div>
              </div>
              <div class="flow-arrow">
                <el-icon><ArrowDown /></el-icon>
              </div>
              <div class="unified-account">
                <div class="account-header">
                  <el-icon><Wallet /></el-icon>
                  <span>分账系统统一管理</span>
                </div>
                <div class="account-balance">¥285,580</div>
                <div class="account-actions">
                  <el-button size="small" type="success">智能分账</el-button>
                  <el-button size="small" type="primary">实时提现</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { Star, VideoCamera, Lock, Check, ArrowDown, Wallet } from '@element-plus/icons-vue'

const openRegister = () => {
  try {
    // 动态导入设备检测工具
    import('../utils/deviceDetect.js').then(({ redirectToRegister }) => {
      const registerUrl = redirectToRegister()
      window.open(registerUrl, '_blank')
    })
  } catch (error) {
    console.error('跳转注册页面失败:', error)
  }
}

const watchDemo = () => {
  console.log('观看产品演示')
}
</script>

<style scoped>
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #10b981 100%);
  overflow: hidden;
}

.hero::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 40%;
  height: 200%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  transform: rotate(15deg);
  pointer-events: none;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.hero-pattern {
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 25% 25%, white 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, white 2px, transparent 2px);
  background-size: 60px 60px;
  background-position: 0 0, 30px 30px;
}

.hero-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 6rem;
  align-items: center;
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
}

.hero-text {
  color: white;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.highlight {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.3rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  color: rgba(255, 255, 255, 0.9);
}

.hero-stats {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 3rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #fbbf24;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.5rem;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: rgba(255, 255, 255, 0.3);
}

.hero-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.cta-primary {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border: none;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
}

.cta-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 14px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
}

.trust-indicators {
  display: flex;
  gap: 2rem;
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
}

.trust-icon {
  color: #10b981;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.dashboard-mockup {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  width: 100%;
  max-width: 500px;
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
}

.mockup-header {
  background: #f8fafc;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mockup-controls {
  display: flex;
  gap: 0.5rem;
}

.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control.red { background: #ef4444; }
.control.yellow { background: #f59e0b; }
.control.green { background: #10b981; }

.mockup-title {
  font-weight: 600;
  color: #374151;
}

.mockup-content {
  padding: 2rem;
}

.platform-cards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.platform-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.platform-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.platform-icon.douyin { background: linear-gradient(135deg, #ff0050, #ff4081); }
.platform-icon.tmall { background: linear-gradient(135deg, #ff6600, #ff8533); }
.platform-icon.jd { background: linear-gradient(135deg, #e1251b, #ff4444); }

.platform-info {
  flex: 1;
}

.platform-name {
  font-size: 0.9rem;
  color: #6b7280;
}

.platform-amount {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.active {
  background: #10b981;
}

.flow-arrow {
  text-align: center;
  color: #6b7280;
  font-size: 1.5rem;
  margin: 1rem 0;
}

.unified-account {
  background: linear-gradient(135deg, #1e3a8a, #3b82f6);
  color: white;
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
}

.account-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.account-balance {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.account-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .stat-divider {
    width: 40px;
    height: 1px;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .trust-indicators {
    justify-content: center;
    flex-wrap: wrap;
  }

  .dashboard-mockup {
    transform: none;
    max-width: 400px;
  }
}
</style>
