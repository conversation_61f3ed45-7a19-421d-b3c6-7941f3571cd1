<template>
  <section class="pain-point-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">电商资金管理痛点</h2>
        <p class="section-subtitle">
          传统资金管理方式存在诸多问题，严重影响企业运营效率
        </p>
      </div>
      
      <div class="pain-points-grid">
        <div class="pain-point-card" v-for="(point, index) in painPoints" :key="index">
          <div class="card-header">
            <div class="pain-icon">
              <el-icon :size="32">
                <component :is="point.icon" />
              </el-icon>
            </div>
            <h3 class="pain-title">{{ point.title }}</h3>
          </div>
          
          <div class="pain-description">
            {{ point.description }}
          </div>
          
          <div class="pain-details">
            <ul class="pain-list">
              <li v-for="detail in point.details" :key="detail">
                <el-icon class="list-icon"><Close /></el-icon>
                {{ detail }}
              </li>
            </ul>
          </div>
          
          <div class="impact-metrics">
            <div class="metric-item">
              <span class="metric-label">影响程度</span>
              <div class="metric-bar">
                <div class="metric-fill" :style="{ width: point.impact + '%' }"></div>
              </div>
              <span class="metric-value">{{ point.impact }}%</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 传统方式 vs 虚拟公户对比 -->
      <div class="comparison-section">
        <h3 class="comparison-title">传统方式 VS 分账系统</h3>
        <div class="comparison-grid">
          <div class="comparison-column traditional">
            <div class="column-header">
              <el-icon class="header-icon"><Warning /></el-icon>
              <h4>传统公户管理</h4>
            </div>
            <div class="comparison-items">
              <div class="comparison-item negative" v-for="item in traditionalIssues" :key="item">
                <el-icon class="item-icon"><Close /></el-icon>
                <span>{{ item }}</span>
              </div>
            </div>
          </div>
          
          <div class="vs-divider">
            <div class="vs-circle">VS</div>
          </div>
          
          <div class="comparison-column virtual">
            <div class="column-header">
              <el-icon class="header-icon"><CircleCheckFilled /></el-icon>
              <h4>分账系统解决方案</h4>
            </div>
            <div class="comparison-items">
              <div class="comparison-item positive" v-for="item in virtualSolutions" :key="item">
                <el-icon class="item-icon"><Check /></el-icon>
                <span>{{ item }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  Money,
  Timer,
  Document,
  Warning,
  Close,
  Check,
  CircleCheckFilled
} from '@element-plus/icons-vue'

const painPoints = [
  {
    icon: Money,
    title: '多平台资金分散',
    description: '抖音、天猫、京东等平台收入无法统一管理，资金分散在多个账户',
    details: [
      '需要登录多个平台查看收入',
      '资金调配困难，影响周转',
      '无法统一进行财务规划',
      '增加财务管理复杂度'
    ],
    impact: 85
  },
  {
    icon: Timer,
    title: '提现限制与效率低',
    description: '传统公户提现额度受限、到账慢，影响资金使用效率',
    details: [
      'T+1或更长到账时间',
      '单日提现额度限制',
      '节假日无法提现',
      '紧急用款时资金不到位'
    ],
    impact: 78
  },
  {
    icon: Document,
    title: '公转私操作复杂',
    description: '薪资发放、供应商付款需要多次人工操作，效率低下',
    details: [
      '需要逐笔手动转账',
      '批量操作支持有限',
      '审批流程繁琐',
      '容易出现操作错误'
    ],
    impact: 72
  },
  {
    icon: Warning,
    title: '财务管理复杂',
    description: '多平台流水汇总导致财务管理复杂，增加运营成本',
    details: [
      '流水记录分散难整理',
      '财务数据统计困难',
      '对账工作量巨大',
      '管理成本高昂'
    ],
    impact: 90
  }
]

const traditionalIssues = [
  '资金分散在多个平台',
  'T+1到账，资金周转慢',
  '手动对账，效率低下',
  '提现额度限制',
  '财务处理复杂',
  '人工操作易出错',
  '无法实时监控资金',
  '管理成本高昂'
]

const virtualSolutions = [
  '统一归集所有平台资金',
  'T+0实时到账',
  '自动对账，准确高效',
  '无限制提现',
  '智能财务管理',
  '全自动化操作',
  '实时资金监控',
  '银行级合规保障'
]
</script>

<style scoped>
.pain-point-section {
  padding: 100px 0;
  background: white;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.pain-points-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 5rem;
}

.pain-point-card {
  background: #fff;
  border: 2px solid #fee2e2;
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.pain-point-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.pain-point-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(239, 68, 68, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.pain-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  border-radius: 12px;
  color: #dc2626;
}

.pain-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #dc2626;
  margin: 0;
}

.pain-description {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.pain-list {
  list-style: none;
  margin-bottom: 1.5rem;
}

.pain-list li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.8rem;
  color: var(--text-color);
  font-size: 0.9rem;
}

.list-icon {
  color: #dc2626;
  font-size: 1rem;
}

.impact-metrics {
  border-top: 1px solid #fee2e2;
  padding-top: 1rem;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.metric-label {
  font-size: 0.9rem;
  color: var(--text-light);
  min-width: 60px;
}

.metric-bar {
  flex: 1;
  height: 8px;
  background: #fee2e2;
  border-radius: 4px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-radius: 4px;
  transition: width 1s ease;
}

.metric-value {
  font-weight: 600;
  color: #dc2626;
  min-width: 40px;
}

.comparison-section {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 20px;
  padding: 3rem;
  margin-top: 3rem;
}

.comparison-title {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 3rem;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: start;
}

.comparison-column {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.traditional {
  border-left: 4px solid #ef4444;
}

.virtual {
  border-left: 4px solid #10b981;
}

.column-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.header-icon {
  font-size: 1.5rem;
}

.traditional .header-icon {
  color: #ef4444;
}

.virtual .header-icon {
  color: #10b981;
}

.column-header h4 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.comparison-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.comparison-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.8rem;
  border-radius: 8px;
  font-size: 0.9rem;
}

.comparison-item.negative {
  background: rgba(239, 68, 68, 0.05);
  color: #7f1d1d;
}

.comparison-item.positive {
  background: rgba(16, 185, 129, 0.05);
  color: #064e3b;
}

.item-icon {
  font-size: 1rem;
}

.negative .item-icon {
  color: #ef4444;
}

.positive .item-icon {
  color: #10b981;
}

.vs-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.vs-circle {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.2rem;
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pain-point-section {
    padding: 80px 0;
  }

  .pain-points-grid {
    grid-template-columns: 1fr;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .vs-divider {
    order: 2;
    height: auto;
    padding: 1rem 0;
  }

  .virtual {
    order: 3;
  }
}
</style>
