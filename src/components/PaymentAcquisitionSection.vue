<template>
  <section class="payment-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">收单服务</h2>
        <p class="section-subtitle">
          专业的支付收单解决方案，支持多渠道、多场景的支付需求
        </p>
      </div>
      
      <div class="content-layout">
        <!-- 左侧产品介绍 -->
        <div class="product-intro">
          <div class="intro-card">
            <div class="card-header">
              <div class="product-icon">
                <el-icon :size="60">
                  <CreditCard />
                </el-icon>
              </div>
              <div class="product-info">
                <h3 class="product-title">多渠道收单聚合</h3>
                <p class="product-desc">一站式支付收单服务，覆盖线上线下全场景</p>
              </div>
            </div>
            
            <div class="features-list">
              <div class="feature-item" v-for="feature in features" :key="feature.title">
                <div class="feature-icon">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="feature-content">
                  <h4 class="feature-title">{{ feature.title }}</h4>
                  <p class="feature-desc">{{ feature.description }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧优势展示 -->
        <div class="advantages-showcase">
          <div class="advantage-grid">
            <div class="advantage-item" v-for="advantage in advantages" :key="advantage.title">
              <div class="advantage-icon">
                <el-icon :size="32">
                  <component :is="advantage.icon" />
                </el-icon>
              </div>
              <h4 class="advantage-title">{{ advantage.title }}</h4>
              <p class="advantage-desc">{{ advantage.description }}</p>
              <div class="advantage-metrics">
                <div class="metric" v-for="metric in advantage.metrics" :key="metric.label">
                  <span class="metric-value">{{ metric.value }}</span>
                  <span class="metric-label">{{ metric.label }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 支付方式展示 -->
      <div class="payment-methods">
        <h3 class="methods-title">支持的支付方式</h3>
        <div class="methods-grid">
          <div class="method-item" v-for="method in paymentMethods" :key="method.name">
            <div class="method-icon">
              <el-icon :size="24">
                <component :is="method.icon" />
              </el-icon>
            </div>
            <span class="method-name">{{ method.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  CreditCard,
  Check,
  Timer,
  Lock,
  TrendCharts,
  Connection,
  Wallet,
  Phone
} from '@element-plus/icons-vue'

// 核心功能特性
const features = [
  {
    title: '全渠道支付接入',
    description: '支持微信支付、支付宝、银联、网银等主流支付方式，一次接入全覆盖'
  },
  {
    title: '智能路由分发',
    description: '根据交易金额、用户偏好、成功率等因素智能选择最优支付通道'
  },
  {
    title: '实时交易监控',
    description: '7×24小时实时监控交易状态，异常交易秒级预警和处理'
  },
  {
    title: '灵活费率配置',
    description: '支持阶梯费率、行业费率等多种计费模式，降低支付成本'
  }
]

// 核心优势
const advantages = [
  {
    icon: Timer,
    title: '极速到账',
    description: '支持T+0、T+1多种结算模式',
    metrics: [
      { value: '秒级', label: '到账速度' },
      { value: '99.9%', label: '成功率' }
    ]
  },
  {
    icon: Lock,
    title: '安全可靠',
    description: '银行级安全保障，多重风控防护',
    metrics: [
      { value: '0', label: '资金风险' },
      { value: '256位', label: 'SSL加密' }
    ]
  },
  {
    icon: TrendCharts,
    title: '数据分析',
    description: '实时交易数据分析，助力业务决策',
    metrics: [
      { value: '实时', label: '数据更新' },
      { value: '多维度', label: '分析报表' }
    ]
  },
  {
    icon: Connection,
    title: '快速接入',
    description: 'API/SDK多种接入方式，快速上线',
    metrics: [
      { value: '1天', label: '接入时间' },
      { value: '7×24', label: '技术支持' }
    ]
  }
]

// 支付方式
const paymentMethods = [
  { name: '微信支付', icon: Phone },
  { name: '支付宝', icon: Wallet },
  { name: '银联支付', icon: CreditCard },
  { name: '网银支付', icon: Connection },
  { name: 'Apple Pay', icon: Phone },
  { name: '京东支付', icon: Wallet }
]
</script>

<style scoped>
.payment-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--text-light);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.content-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 5rem;
}

.intro-card {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(59, 130, 246, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2.5rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.product-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 16px;
  color: white;
}

.product-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.product-desc {
  color: var(--text-light);
  font-size: 1rem;
  line-height: 1.5;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(34, 197, 94, 0.1);
  border-radius: 8px;
  color: #22c55e;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.feature-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.feature-desc {
  color: var(--text-light);
  line-height: 1.5;
  font-size: 0.95rem;
}

.advantage-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.advantage-item {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.1);
  text-align: center;
  transition: all 0.3s ease;
}

.advantage-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(59, 130, 246, 0.12);
}

.advantage-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 12px;
  color: white;
  margin: 0 auto 1rem;
}

.advantage-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.8rem;
}

.advantage-desc {
  color: var(--text-light);
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1.5rem;
}

.advantage-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.metric-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.metric-label {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 0.2rem;
}

.payment-methods {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

.methods-title {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 2rem;
}

.methods-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 2rem;
}

.method-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.8rem;
  padding: 1.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.method-item:hover {
  background: rgba(59, 130, 246, 0.05);
}

.method-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 12px;
  color: white;
}

.method-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .advantage-grid {
    grid-template-columns: 1fr;
  }
  
  .methods-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .payment-section {
    padding: 80px 0;
  }
  
  .intro-card {
    padding: 2rem;
  }
  
  .card-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .methods-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .advantage-metrics {
    grid-template-columns: 1fr;
  }
}
</style>
