<template>
  <section class="solution-section">
    <div class="container-wide">
      <div class="section-header">
        <h2 class="section-title">核心产品与服务</h2>
        <p class="section-subtitle">
          基于银行级技术架构，为电商企业提供全方位的金融科技解决方案
        </p>
      </div>

      <!-- 分账系统主要产品 (40%) -->
      <div class="main-product-section">
        <div class="main-product-card">
          <div class="product-header">
            <div class="product-icon">
              <el-icon :size="64">
                <component :is="mainProduct.icon" />
              </el-icon>
            </div>
            <div class="product-info">
              <h3 class="product-title">{{ mainProduct.title }}</h3>
              <p class="product-description">{{ mainProduct.description }}</p>
            </div>
          </div>

          <div class="product-features">
            <div class="feature-grid">
              <div class="feature-item" v-for="feature in mainProduct.features" :key="feature.name">
                <el-icon class="feature-icon"><Check /></el-icon>
                <div class="feature-content">
                  <span class="feature-name">{{ feature.name }}</span>
                  <span class="feature-desc">{{ feature.desc }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="product-metrics">
            <div class="metric-item" v-for="metric in mainProduct.metrics" :key="metric.label">
              <div class="metric-value">{{ metric.value }}</div>
              <div class="metric-label">{{ metric.label }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 其他产品服务 (各20%) -->
      <div class="other-products-section">
        <div class="products-grid">
          <div class="product-card" v-for="(product, index) in otherProducts" :key="index">
            <div class="card-header">
              <div class="product-icon-small">
                <el-icon :size="40">
                  <component :is="product.icon" />
                </el-icon>
              </div>
              <div class="product-badge">{{ product.badge }}</div>
            </div>

            <div class="card-content">
              <h4 class="product-title">{{ product.title }}</h4>
              <p class="product-description">{{ product.description }}</p>

              <div class="product-highlights">
                <div class="highlight-item" v-for="highlight in product.highlights" :key="highlight">
                  <el-icon class="highlight-icon"><Check /></el-icon>
                  <span>{{ highlight }}</span>
                </div>
              </div>

              <div class="product-metrics-small">
                <div class="metric-item" v-for="metric in product.metrics" :key="metric.label">
                  <div class="metric-value">{{ metric.value }}</div>
                  <div class="metric-label">{{ metric.label }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </section>
</template>

<script setup>
import {
  Connection,
  Timer,
  Lock,
  TrendCharts,
  Check
} from '@element-plus/icons-vue'

// 主要产品 - 分账系统 (40%)
const mainProduct = {
  icon: TrendCharts,
  title: '智能分账系统',
  description: '基于预设规则自动分账，支持供应商、员工、合作伙伴批量结算，是我们的核心产品服务',
  features: [
    {
      name: '灵活规则引擎',
      desc: '支持复杂分账规则配置，满足各种业务场景'
    },
    {
      name: '批量处理能力',
      desc: '支持千笔订单同时分账，高并发处理'
    },
    {
      name: '实时状态监控',
      desc: '分账状态实时跟踪和通知，全程透明'
    },
    {
      name: '自动对账功能',
      desc: '智能对账系统，确保资金流转准确无误'
    },
    {
      name: '异常处理机制',
      desc: '完善的异常处理和风控机制，保障资金安全'
    },
    {
      name: '多维度报表',
      desc: '提供详细的分账报表和数据分析'
    }
  ],
  metrics: [
    { value: '1000+', label: '并发处理' },
    { value: '毫秒级', label: '处理速度' },
    { value: '100%', label: '准确率' },
    { value: '7×24', label: '服务时间' }
  ]
}

// 其他产品服务 (各20%)
const otherProducts = [
  {
    icon: Connection,
    badge: '收单服务',
    title: '多渠道收单聚合',
    description: '一键连接20+主流电商平台，实现收单资金统一归集管理',
    highlights: [
      '全渠道覆盖支持',
      'API快速接入',
      '实时清算监控'
    ],
    metrics: [
      { value: '20+', label: '收单渠道' },
      { value: '99.9%', label: '成功率' }
    ]
  },
  {
    icon: Timer,
    badge: '供应链金融',
    title: '供应链金融服务',
    description: '基于真实交易数据，为供应链上下游企业提供灵活的金融服务',
    highlights: [
      '应收账款融资',
      '库存资产盘活',
      '智能信用评估'
    ],
    metrics: [
      { value: '72小时', label: '放款时效' },
      { value: '500万', label: '最高额度' }
    ]
  },
  {
    icon: Lock,
    badge: '跨境结汇',
    title: '跨境结汇服务',
    description: '专业的跨境电商结汇服务，支持多币种收款和结汇',
    highlights: [
      '多币种支持',
      '实时汇率查询',
      '合规结汇流程'
    ],
    metrics: [
      { value: '30+', label: '支持币种' },
      { value: 'T+1', label: '结汇时效' }
    ]
  }
]
</script>

<style scoped>
.solution-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

/* 主要产品区域 (40%) */
.main-product-section {
  margin-bottom: 5rem;
}

.main-product-card {
  background: white;
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.main-product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.product-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2.5rem;
}

.product-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 24px;
  color: white;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
}

.product-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.product-description {
  font-size: 1.1rem;
  color: var(--text-light);
  line-height: 1.6;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.feature-icon {
  color: var(--accent-color);
  font-size: 1.2rem;
  margin-top: 0.2rem;
  flex-shrink: 0;
}

.feature-content {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.feature-name {
  font-weight: 600;
  color: var(--text-color);
}

.feature-desc {
  font-size: 0.9rem;
  color: var(--text-light);
  line-height: 1.4;
}

.product-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  padding: 2rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 16px;
}

.metric-item {
  text-align: center;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 500;
}

/* 其他产品区域 (各20%) */
.other-products-section {
  margin-top: 3rem;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.product-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(59, 130, 246, 0.12);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.product-icon-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 12px;
  color: white;
}

.product-badge {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.card-content .product-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.card-content .product-description {
  font-size: 0.95rem;
  color: var(--text-light);
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.product-highlights {
  margin-bottom: 1.5rem;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 0.8rem;
  font-size: 0.9rem;
}

.highlight-icon {
  color: var(--accent-color);
  font-size: 1rem;
  flex-shrink: 0;
}

.product-metrics-small {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 12px;
}

.product-metrics-small .metric-item {
  text-align: center;
}

.product-metrics-small .metric-value {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.3rem;
}

.product-metrics-small .metric-label {
  font-size: 0.8rem;
  color: var(--text-light);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .product-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .solution-section {
    padding: 80px 0;
  }

  .main-product-card {
    padding: 2rem;
  }

  .product-header {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .product-title {
    font-size: 1.5rem;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .product-metrics {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .product-card {
    padding: 1.5rem;
  }

  .card-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
</style>
