<template>
  <section class="solution-section">
    <div class="container-wide">
      <div class="section-header">
        <h2 class="section-title">分账系统核心优势</h2>
        <p class="section-subtitle">
          基于银行级技术架构，为电商企业提供全方位的分账管理解决方案
        </p>
      </div>
      
      <div class="advantages-grid">
        <div class="advantage-card" v-for="(advantage, index) in advantages" :key="index">
          <div class="card-visual">
            <div class="advantage-icon">
              <el-icon :size="48">
                <component :is="advantage.icon" />
              </el-icon>
            </div>
            <div class="advantage-badge">{{ advantage.badge }}</div>
          </div>
          
          <div class="card-content">
            <h3 class="advantage-title">{{ advantage.title }}</h3>
            <p class="advantage-description">{{ advantage.description }}</p>
            
            <div class="advantage-features">
              <div class="feature-item" v-for="feature in advantage.features" :key="feature.name">
                <el-icon class="feature-icon"><Check /></el-icon>
                <div class="feature-content">
                  <span class="feature-name">{{ feature.name }}</span>
                  <span class="feature-desc">{{ feature.desc }}</span>
                </div>
              </div>
            </div>
            
            <div class="advantage-metrics" v-if="advantage.metrics">
              <div class="metric-item" v-for="metric in advantage.metrics" :key="metric.label">
                <div class="metric-value">{{ metric.value }}</div>
                <div class="metric-label">{{ metric.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </section>
</template>

<script setup>
import {
  Connection,
  Timer,
  Lock,
  TrendCharts,
  Check
} from '@element-plus/icons-vue'

const advantages = [
  {
    icon: Connection,
    badge: '收单服务',
    title: '多渠道收单聚合',
    description: '一键连接20+主流电商平台，实现收单资金统一归集管理',
    features: [
      {
        name: '全渠道覆盖',
        desc: '支持线上线下、移动支付等多种收单方式'
      },
      {
        name: 'API快速接入',
        desc: '0代码接入，5分钟完成收单通道连接'
      },
      {
        name: '实时清算',
        desc: '24小时实时监控收单数据和资金流向'
      }
    ],
    metrics: [
      { value: '20+', label: '收单渠道' },
      { value: '5分钟', label: '接入时间' },
      { value: '99.9%', label: '成功率' }
    ]
  },
  {
    icon: Timer,
    badge: '供应链金融',
    title: '供应链金融服务',
    description: '基于真实交易数据，为供应链上下游企业提供灵活的金融服务',
    features: [
      {
        name: '应收账款融资',
        desc: '基于真实订单快速获得资金支持'
      },
      {
        name: '库存融资',
        desc: '盘活库存资产，提升资金周转效率'
      },
      {
        name: '信用评估',
        desc: '基于交易数据智能评估企业信用'
      }
    ],
    metrics: [
      { value: '72小时', label: '放款时效' },
      { value: '500万', label: '最高额度' },
      { value: '年化6%起', label: '融资成本' }
    ]
  },
  {
    icon: TrendCharts,
    badge: '智能分账',
    title: '智能分账系统',
    description: '基于预设规则自动分账，支持供应商、员工、合作伙伴批量结算',
    features: [
      {
        name: '规则引擎',
        desc: '灵活配置分账规则和比例'
      },
      {
        name: '批量处理',
        desc: '支持千笔订单同时分账'
      },
      {
        name: '实时监控',
        desc: '分账状态实时跟踪和通知'
      }
    ],
    metrics: [
      { value: '1000+', label: '并发处理' },
      { value: '毫秒级', label: '处理速度' },
      { value: '100%', label: '准确率' }
    ]
  },
  {
    icon: Lock,
    badge: '跨境结汇',
    title: '跨境结汇服务',
    description: '专业的跨境电商结汇服务，支持多币种收款和结汇',
    features: [
      {
        name: '多币种支持',
        desc: '支持美元、欧元、英镑等主流货币'
      },
      {
        name: '实时汇率',
        desc: '提供实时汇率查询和锁汇服务'
      },
      {
        name: '合规结汇',
        desc: '符合外汇管理规定的合规结汇流程'
      }
    ],
    metrics: [
      { value: '30+', label: '支持币种' },
      { value: 'T+1', label: '结汇时效' },
      { value: '优惠汇率', label: '汇率优势' }
    ]
  }
]
</script>

<style scoped>
.solution-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
  margin-bottom: 5rem;
}

@media (max-width: 1024px) {
  .advantages-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

.advantage-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.advantage-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.advantage-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.15);
}

.card-visual {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.advantage-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 20px;
  color: white;
}

.advantage-badge {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.advantage-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.advantage-description {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.advantage-features {
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.feature-icon {
  color: var(--secondary-color);
  font-size: 1.2rem;
  margin-top: 0.2rem;
}

.feature-content {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.feature-name {
  font-weight: 600;
  color: var(--text-color);
}

.feature-desc {
  font-size: 0.9rem;
  color: var(--text-light);
}

.advantage-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.metric-item {
  text-align: center;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.metric-label {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 0.3rem;
}

.architecture-section {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

.architecture-title {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 3rem;
}

.architecture-diagram {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.arch-layer {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 16px;
  padding: 2rem;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.arch-layer:hover {
  border-color: var(--accent-color);
  transform: translateY(-5px);
}

.layer-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.layer-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.layer-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.layer-components {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.component-item {
  background: white;
  padding: 0.8rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  color: var(--text-color);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.component-item:hover {
  border-color: var(--accent-color);
  background: rgba(59, 130, 246, 0.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .solution-section {
    padding: 80px 0;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .advantage-metrics {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .architecture-diagram {
    grid-template-columns: 1fr;
  }
}
</style>
