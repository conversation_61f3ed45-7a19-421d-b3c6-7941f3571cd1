<template>
  <section class="solution-section">
    <div class="container-wide">
      <div class="section-header">
        <h2 class="section-title">分账系统核心优势</h2>
        <p class="section-subtitle">
          基于银行级技术架构，为电商企业提供全方位的分账管理解决方案
        </p>
      </div>

      <div class="advantages-grid">
        <div class="advantage-card" v-for="(advantage, index) in advantages" :key="index">
          <div class="card-visual">
            <div class="advantage-icon">
              <el-icon :size="48">
                <component :is="advantage.icon" />
              </el-icon>
            </div>
            <div class="advantage-badge">{{ advantage.badge }}</div>
          </div>

          <div class="card-content">
            <h3 class="advantage-title">{{ advantage.title }}</h3>
            <p class="advantage-description">{{ advantage.description }}</p>

            <div class="advantage-features">
              <div class="feature-item" v-for="feature in advantage.features" :key="feature.name">
                <el-icon class="feature-icon"><Check /></el-icon>
                <div class="feature-content">
                  <span class="feature-name">{{ feature.name }}</span>
                  <span class="feature-desc">{{ feature.desc }}</span>
                </div>
              </div>
            </div>

            <div class="advantage-metrics" v-if="advantage.metrics">
              <div class="metric-item" v-for="metric in advantage.metrics" :key="metric.label">
                <div class="metric-value">{{ metric.value }}</div>
                <div class="metric-label">{{ metric.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </section>
</template>

<script setup>
import {
  Connection,
  Timer,
  Lock,
  TrendCharts,
  Check
} from '@element-plus/icons-vue'

const advantages = [
  {
    icon: TrendCharts,
    badge: '智能分账',
    title: '智能分账系统',
    description: '基于预设规则自动分账，支持供应商、员工、合作伙伴批量结算',
    features: [
      {
        name: '规则引擎',
        desc: '灵活配置分账规则和比例'
      },
      {
        name: '批量处理',
        desc: '支持千笔订单同时分账'
      },
      {
        name: '实时监控',
        desc: '分账状态实时跟踪和通知'
      }
    ],
    metrics: [
      { value: '1000+', label: '并发处理' },
      { value: '毫秒级', label: '处理速度' },
      { value: '100%', label: '准确率' }
    ]
  },
  {
    icon: Connection,
    badge: '资金聚合',
    title: '多平台资金聚合',
    description: '一键连接20+主流电商平台，实现资金统一归集管理',
    features: [
      {
        name: '平台覆盖全面',
        desc: '支持抖音、快手、天猫、京东等主流平台'
      },
      {
        name: 'API自动对接',
        desc: '0代码接入，5分钟完成平台连接'
      },
      {
        name: '实时数据同步',
        desc: '24小时实时监控平台收入变化'
      }
    ],
    metrics: [
      { value: '20+', label: '支持平台' },
      { value: '5分钟', label: '接入时间' },
      { value: '99.9%', label: '数据准确率' }
    ]
  },
  {
    icon: Timer,
    badge: '效率提升',
    title: 'T+0实时到账',
    description: '突破传统限制，资金实时到账，提升资金使用效率',
    features: [
      {
        name: '无限制提现',
        desc: '突破单日提现额度限制'
      },
      {
        name: '7×24小时服务',
        desc: '节假日也能正常提现到账'
      },
      {
        name: '秒级到账',
        desc: '最快3秒内完成资金到账'
      }
    ],
    metrics: [
      { value: 'T+0', label: '到账时效' },
      { value: '3秒', label: '最快到账' },
      { value: '无限制', label: '提现额度' }
    ]
  },
  {
    icon: Lock,
    badge: '安全保障',
    title: '银行级安全',
    description: '央行监管的银行级安全保障，资金安全有保障',
    features: [
      {
        name: '央行监管',
        desc: '受央行监管，合规性有保障'
      },
      {
        name: '资金隔离',
        desc: '客户资金与平台资金完全隔离'
      },
      {
        name: '多重验证',
        desc: '短信+人脸识别双重安全验证'
      }
    ],
    metrics: [
      { value: '银行级', label: '安全等级' },
      { value: '100%', label: '资金隔离' },
      { value: '0', label: '资金风险' }
    ]
  }
]
</script>

<style scoped>
.solution-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
  margin-bottom: 5rem;
}

@media (max-width: 1024px) {
  .advantages-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

.advantage-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.advantage-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.advantage-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(59, 130, 246, 0.15);
}

.card-visual {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.advantage-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 20px;
  color: white;
}

.advantage-badge {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.advantage-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.advantage-description {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.advantage-features {
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.feature-icon {
  color: var(--secondary-color);
  font-size: 1.2rem;
  margin-top: 0.2rem;
}

.feature-content {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.feature-name {
  font-weight: 600;
  color: var(--text-color);
}

.feature-desc {
  font-size: 0.9rem;
  color: var(--text-light);
}

.advantage-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.metric-item {
  text-align: center;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.metric-label {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 0.3rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .solution-section {
    padding: 80px 0;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .advantage-metrics {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}
</style>
