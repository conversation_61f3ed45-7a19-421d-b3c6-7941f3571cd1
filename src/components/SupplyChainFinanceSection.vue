<template>
  <section class="finance-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">供应链金融</h2>
        <p class="section-subtitle">
          基于真实交易数据，为供应链上下游企业提供灵活的金融服务
        </p>
      </div>
      
      <div class="content-layout">
        <!-- 左侧产品介绍 -->
        <div class="product-showcase">
          <div class="showcase-card">
            <div class="card-header">
              <div class="product-icon">
                <el-icon :size="60">
                  <TrendCharts />
                </el-icon>
              </div>
              <div class="product-info">
                <h3 class="product-title">供应链金融服务</h3>
                <p class="product-desc">盘活供应链资产，提升资金周转效率</p>
              </div>
            </div>
            
            <div class="finance-products">
              <div class="finance-item" v-for="product in financeProducts" :key="product.title">
                <div class="finance-icon">
                  <el-icon :size="24">
                    <component :is="product.icon" />
                  </el-icon>
                </div>
                <div class="finance-content">
                  <h4 class="finance-title">{{ product.title }}</h4>
                  <p class="finance-desc">{{ product.description }}</p>
                  <div class="finance-features">
                    <span v-for="feature in product.features" :key="feature" class="feature-tag">
                      {{ feature }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧流程展示 -->
        <div class="process-showcase">
          <h3 class="process-title">融资流程</h3>
          <div class="process-steps">
            <div class="step-item" v-for="(step, index) in processSteps" :key="step.title">
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <h4 class="step-title">{{ step.title }}</h4>
                <p class="step-desc">{{ step.description }}</p>
                <div class="step-time">{{ step.timeframe }}</div>
              </div>
            </div>
          </div>
          
          <div class="key-metrics">
            <div class="metric-item" v-for="metric in keyMetrics" :key="metric.label">
              <div class="metric-value">{{ metric.value }}</div>
              <div class="metric-label">{{ metric.label }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 优势特点 -->
      <div class="advantages-section">
        <h3 class="advantages-title">核心优势</h3>
        <div class="advantages-grid">
          <div class="advantage-card" v-for="advantage in advantages" :key="advantage.title">
            <div class="advantage-icon">
              <el-icon :size="32">
                <component :is="advantage.icon" />
              </el-icon>
            </div>
            <h4 class="advantage-title">{{ advantage.title }}</h4>
            <p class="advantage-desc">{{ advantage.description }}</p>
            <ul class="advantage-points">
              <li v-for="point in advantage.points" :key="point">{{ point }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  TrendCharts,
  Wallet,
  DocumentCopy,
  Timer,
  Lock,
  DataBoard,
  Connection,
  Check
} from '@element-plus/icons-vue'

// 金融产品
const financeProducts = [
  {
    icon: DocumentCopy,
    title: '应收账款融资',
    description: '基于真实应收账款提供融资服务，盘活企业流动资金',
    features: ['最高90%融资比例', '年化利率6%起', '最快当日放款']
  },
  {
    icon: Wallet,
    title: '库存融资',
    description: '以库存商品作为质押，获得流动资金支持',
    features: ['灵活质押比例', '随借随还', '降低库存成本']
  },
  {
    icon: TrendCharts,
    title: '订单融资',
    description: '基于真实订单数据，提前获得资金支持',
    features: ['订单确认即放款', '支持批量融资', '提升履约能力']
  }
]

// 融资流程
const processSteps = [
  {
    icon: DocumentCopy,
    title: '在线申请',
    description: '提交融资申请，上传相关资料',
    timeframe: '5分钟'
  },
  {
    icon: TrendCharts,
    title: '智能审核',
    description: '基于交易数据智能评估信用',
    timeframe: '2小时'
  },
  {
    icon: Lock,
    title: '额度授信',
    description: '系统自动核定融资额度',
    timeframe: '24小时'
  },
  {
    icon: Wallet,
    title: '资金放款',
    description: '确认用款需求，快速放款',
    timeframe: '1小时'
  }
]

// 关键指标
const keyMetrics = [
  { value: '72小时', label: '最快放款' },
  { value: '500万', label: '最高额度' },
  { value: '年化6%起', label: '融资成本' },
  { value: '90%', label: '最高融资比例' }
]

// 核心优势
const advantages = [
  {
    icon: Timer,
    title: '快速审批',
    description: '基于大数据风控模型，实现快速审批放款',
    points: [
      '智能风控评估',
      '自动化审批流程',
      '最快当日放款',
      '7×24小时服务'
    ]
  },
  {
    icon: Lock,
    title: '风险可控',
    description: '多维度风险评估，确保资金安全',
    points: [
      '真实交易背景',
      '多重风控措施',
      '动态风险监控',
      '专业风险团队'
    ]
  },
  {
    icon: DataBoard,
    title: '灵活便捷',
    description: '多种融资产品，满足不同资金需求',
    points: [
      '多样化产品',
      '灵活还款方式',
      '随借随还',
      '循环授信'
    ]
  },
  {
    icon: Connection,
    title: '生态协同',
    description: '与供应链各环节深度融合，提升整体效率',
    points: [
      '上下游协同',
      '数据互通共享',
      '降低融资成本',
      '提升供应链效率'
    ]
  }
]
</script>

<style scoped>
.finance-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #d97706;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--text-light);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.content-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 5rem;
}

.showcase-card {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(217, 119, 6, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2.5rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.product-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #d97706, #b45309);
  border-radius: 16px;
  color: white;
}

.product-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.product-desc {
  color: var(--text-light);
  font-size: 1rem;
  line-height: 1.5;
}

.finance-products {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.finance-item {
  display: flex;
  gap: 1.5rem;
  padding: 1.5rem;
  background: rgba(217, 119, 6, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(217, 119, 6, 0.1);
}

.finance-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #d97706, #b45309);
  border-radius: 12px;
  color: white;
  flex-shrink: 0;
}

.finance-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.finance-desc {
  color: var(--text-light);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.finance-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.feature-tag {
  background: rgba(217, 119, 6, 0.1);
  color: #d97706;
  padding: 0.3rem 0.8rem;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
}

.process-showcase {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
}

.process-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 2rem;
  text-align: center;
}

.process-steps {
  margin-bottom: 3rem;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 20px;
  top: 50px;
  width: 2px;
  height: 40px;
  background: linear-gradient(to bottom, #d97706, rgba(217, 119, 6, 0.3));
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #d97706, #b45309);
  border-radius: 50%;
  color: white;
  font-weight: 700;
  flex-shrink: 0;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.step-desc {
  color: var(--text-light);
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.step-time {
  color: #d97706;
  font-size: 0.9rem;
  font-weight: 600;
}

.key-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  padding: 2rem;
  background: rgba(217, 119, 6, 0.05);
  border-radius: 16px;
}

.metric-item {
  text-align: center;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #d97706;
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 0.9rem;
  color: var(--text-light);
}

.advantages-section {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

.advantages-title {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 2.5rem;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2.5rem;
}

.advantage-card {
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid rgba(217, 119, 6, 0.1);
  background: rgba(217, 119, 6, 0.02);
  transition: all 0.3s ease;
}

.advantage-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(217, 119, 6, 0.1);
}

.advantage-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #d97706, #b45309);
  border-radius: 12px;
  color: white;
  margin-bottom: 1.5rem;
}

.advantage-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.advantage-desc {
  color: var(--text-light);
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.advantage-points {
  list-style: none;
  padding: 0;
  margin: 0;
}

.advantage-points li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  font-size: 0.9rem;
}

.advantage-points li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #d97706;
  font-weight: 700;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .advantages-grid {
    grid-template-columns: 1fr;
  }
  
  .key-metrics {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .finance-section {
    padding: 80px 0;
  }
  
  .showcase-card,
  .process-showcase {
    padding: 2rem;
  }
  
  .card-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .finance-item {
    flex-direction: column;
    text-align: center;
  }
}
</style>
