import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Solutions from '../views/Solutions.vue'
import Scenarios from '../views/Scenarios.vue'
import About from '../views/About.vue'
import PrivacyPolicy from '../views/PrivacyPolicy.vue'
import TermsOfService from '../views/TermsOfService.vue'
import LegalNotice from '../views/LegalNotice.vue'
import LegalPages from '../views/LegalPages.vue'
import LegalDocuments from '../views/LegalDocuments.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/solutions',
    name: 'Solutions',
    component: Solutions
  },
  {
    path: '/scenarios',
    name: 'Scenarios',
    component: Scenarios
  },
  {
    path: '/about',
    name: 'About',
    component: About
  },
  {
    path: '/legal',
    name: 'LegalPages',
    component: LegalPages,
    meta: {
      title: '法律文档中心 - ESoonPay'
    }
  },
  {
    path: '/legal-documents',
    name: 'LegalDocuments',
    component: LegalDocuments,
    meta: {
      title: '法律文档与政策 - ESoonPay'
    }
  },
  {
    path: '/privacy-policy',
    name: 'PrivacyPolicy',
    component: PrivacyPolicy,
    meta: {
      title: '隐私政策 - ESoonPay'
    }
  },
  {
    path: '/terms-of-service',
    name: 'TermsOfService',
    component: TermsOfService,
    meta: {
      title: '服务条款 - ESoonPay'
    }
  },
  {
    path: '/legal-notice',
    name: 'LegalNotice',
    component: LegalNotice,
    meta: {
      title: '法律声明 - ESoonPay'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta && to.meta.title) {
    document.title = to.meta.title
  } else {
    document.title = 'ESoonPay - 专业的电商企业分账系统服务平台'
  }
  next()
})

export default router
