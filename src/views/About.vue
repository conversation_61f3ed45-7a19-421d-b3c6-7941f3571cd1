<template>
  <div class="about-page">
    <!-- 页面头部 -->
    <section class="page-hero">
      <div class="container-wide">
        <div class="hero-content">
          <h1 class="page-title">关于我们</h1>
          <p class="page-subtitle">
            专注于为电商企业提供专业的分账系统服务，让资金管理变得简单高效
          </p>
        </div>
      </div>
    </section>

    <!-- 公司介绍 -->
    <section class="company-intro">
      <div class="container-wide">
        <div class="intro-content">
          <div class="intro-text">
            <h2 class="section-title">企业使命</h2>
            <p class="mission-text">
              让每一家电商企业都能享受到银行级的资金管理服务，通过技术创新降低企业运营成本，
              提升资金使用效率，助力电商行业的数字化转型。
            </p>
            
            <div class="company-values">
              <div class="value-item" v-for="value in companyValues" :key="value.title">
                <div class="value-icon">
                  <el-icon :size="32">
                    <component :is="value.icon" />
                  </el-icon>
                </div>
                <h4 class="value-title">{{ value.title }}</h4>
                <p class="value-desc">{{ value.description }}</p>
              </div>
            </div>
          </div>
          
          <div class="intro-visual">
            <div class="company-stats">
              <div class="stat-item" v-for="stat in companyStats" :key="stat.label">
                <div class="stat-number">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 发展历程 -->
    <section class="timeline-section">
      <div class="container">
        <h2 class="section-title">发展历程</h2>
        <div class="timeline">
          <div class="timeline-item" v-for="(milestone, index) in milestones" :key="index">
            <div class="timeline-marker">
              <div class="marker-dot"></div>
              <div class="marker-line" v-if="index < milestones.length - 1"></div>
            </div>
            <div class="timeline-content">
              <div class="timeline-date">{{ milestone.date }}</div>
              <h4 class="timeline-title">{{ milestone.title }}</h4>
              <p class="timeline-desc">{{ milestone.description }}</p>
              <div class="timeline-achievements" v-if="milestone.achievements">
                <div class="achievement-item" v-for="achievement in milestone.achievements" :key="achievement">
                  <el-icon class="achievement-icon"><Check /></el-icon>
                  {{ achievement }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 团队介绍 -->
    <section class="team-section">
      <div class="container">
        <h2 class="section-title">核心团队</h2>
        <div class="team-grid">
          <div class="team-member" v-for="member in teamMembers" :key="member.name">
            <div class="member-avatar">
              <img :src="member.avatar" :alt="member.name" />
            </div>
            <div class="member-info">
              <h4 class="member-name">{{ member.name }}</h4>
              <div class="member-title">{{ member.title }}</div>
              <p class="member-bio">{{ member.bio }}</p>
              <div class="member-experience">
                <div class="experience-item" v-for="exp in member.experience" :key="exp">
                  {{ exp }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 合作伙伴 -->
    <section class="partners-section">
      <div class="container">
        <h2 class="section-title">合作伙伴</h2>
        <div class="partners-categories">
          <div class="partner-category" v-for="category in partnerCategories" :key="category.name">
            <h3 class="category-title">{{ category.name }}</h3>
            <div class="partners-grid">
              <div class="partner-item" v-for="partner in category.partners" :key="partner.name">
                <div class="partner-logo">
                  <img :src="partner.logo" :alt="partner.name" />
                </div>
                <div class="partner-name">{{ partner.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 资质认证 -->
    <section class="certifications-section">
      <div class="container">
        <h2 class="section-title">资质认证</h2>
        <div class="certifications-grid">
          <div class="cert-item" v-for="cert in certifications" :key="cert.name">
            <div class="cert-icon">
              <el-icon :size="48">
                <component :is="cert.icon" />
              </el-icon>
            </div>
            <h4 class="cert-name">{{ cert.name }}</h4>
            <p class="cert-desc">{{ cert.description }}</p>
            <div class="cert-number">{{ cert.number }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系我们 -->
    <section class="contact-section">
      <div class="container">
        <h2 class="section-title">联系我们</h2>
        <div class="contact-content">
          <div class="contact-info">
            <div class="contact-item" v-for="contact in contactInfo" :key="contact.type">
              <div class="contact-icon">
                <el-icon :size="24">
                  <component :is="contact.icon" />
                </el-icon>
              </div>
              <div class="contact-details">
                <div class="contact-label">{{ contact.label }}</div>
                <div class="contact-value">{{ contact.value }}</div>
              </div>
            </div>
          </div>
          
          <div class="contact-form">
            <h3>留言咨询</h3>
            <el-form :model="contactForm" label-width="80px">
              <el-form-item label="姓名">
                <el-input v-model="contactForm.name" placeholder="请输入您的姓名" />
              </el-form-item>
              <el-form-item label="公司">
                <el-input v-model="contactForm.company" placeholder="请输入公司名称" />
              </el-form-item>
              <el-form-item label="电话">
                <el-input v-model="contactForm.phone" placeholder="请输入联系电话" />
              </el-form-item>
              <el-form-item label="邮箱">
                <el-input v-model="contactForm.email" placeholder="请输入邮箱地址" />
              </el-form-item>
              <el-form-item label="留言">
                <el-input 
                  v-model="contactForm.message" 
                  type="textarea" 
                  :rows="4"
                  placeholder="请描述您的需求或问题"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="submitForm">提交留言</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {
  Lock,
  TrendCharts,
  UserFilled,
  Service,
  Check,
  Phone,
  Message,
  Location,
  Timer,
  Star
} from '@element-plus/icons-vue'

const contactForm = ref({
  name: '',
  company: '',
  phone: '',
  email: '',
  message: ''
})

const companyValues = [
  {
    icon: Lock,
    title: '安全可靠',
    description: '银行级安全保障，资金安全是我们的第一要务'
  },
  {
    icon: TrendCharts,
    title: '创新驱动',
    description: '持续技术创新，为客户提供最优的解决方案'
  },
  {
    icon: UserFilled,
    title: '客户至上',
    description: '以客户需求为导向，提供专业贴心的服务'
  },
  {
    icon: Service,
    title: '专业服务',
    description: '7×24小时专业服务，确保业务连续性'
  }
]

const companyStats = [
  { value: '2019', label: '成立年份' },
  { value: '500+', label: '团队规模' },
  { value: '10,000+', label: '服务企业' },
  { value: '99.9%', label: '系统稳定性' }
]

const milestones = [
  {
    date: '2019年',
    title: '公司成立',
    description: 'ESoonPay正式成立，专注于电商金融服务领域',
    achievements: ['获得天使轮融资', '核心团队组建完成', '产品原型开发']
  },
  {
    date: '2020年',
    title: '产品上线',
    description: '虚拟公户产品正式上线，开始为电商企业提供服务',
    achievements: ['首批100家企业接入', '获得支付牌照', '建立风控体系']
  },
  {
    date: '2021年',
    title: '快速发展',
    description: '业务快速增长，服务企业数量突破1000家',
    achievements: ['完成A轮融资', '团队扩展至200人', '日处理资金过亿']
  },
  {
    date: '2022年',
    title: '技术升级',
    description: '完成技术架构升级，系统性能大幅提升',
    achievements: ['微服务架构重构', '获得ISO27001认证', '服务企业超5000家']
  },
  {
    date: '2023年',
    title: '生态建设',
    description: '构建完整的电商金融服务生态，拓展更多应用场景',
    achievements: ['完成B轮融资', '推出开放平台', '服务企业破万家']
  },
  {
    date: '2024年',
    title: '持续创新',
    description: '继续深耕电商金融领域，为更多企业提供优质服务',
    achievements: ['AI智能风控上线', '国际化业务启动', '新产品持续迭代']
  }
]

const teamMembers = [
  {
    name: '张明',
    title: '创始人 & CEO',
    avatar: '/team/ceo.jpg',
    bio: '前阿里巴巴资深技术专家，拥有15年互联网金融经验',
    experience: ['阿里巴巴 - 技术总监', '蚂蚁金服 - 产品专家', '清华大学 - 计算机硕士']
  },
  {
    name: '李华',
    title: 'CTO',
    avatar: '/team/cto.jpg',
    bio: '前腾讯云架构师，专注于分布式系统和金融科技',
    experience: ['腾讯云 - 首席架构师', '微众银行 - 技术专家', '北京大学 - 软件工程硕士']
  },
  {
    name: '王芳',
    title: 'CFO',
    avatar: '/team/cfo.jpg',
    bio: '前普华永道合伙人，拥有丰富的财务管理和风控经验',
    experience: ['普华永道 - 合伙人', '中信银行 - 风控总监', '复旦大学 - 金融学硕士']
  },
  {
    name: '刘强',
    title: '产品总监',
    avatar: '/team/cpo.jpg',
    bio: '前京东金融产品负责人，深耕电商金融产品设计',
    experience: ['京东金融 - 产品总监', '滴滴出行 - 高级产品经理', '上海交大 - MBA']
  }
]

const partnerCategories = [
  {
    name: '合作银行',
    partners: [
      { name: '百信银行', logo: '/banks/baixin.png' },
      { name: '苏商银行', logo: '/banks/sushang.png' },
      { name: '蓝海银行', logo: '/banks/lanhai.png' },
      { name: '新网银行', logo: '/banks/xinwang.png' }
    ]
  },
  {
    name: '电商平台',
    partners: [
      { name: '抖音电商', logo: '/partners/douyin.png' },
      { name: '天猫', logo: '/partners/tmall.png' },
      { name: '京东', logo: '/partners/jd.png' },
      { name: '拼多多', logo: '/partners/pdd.png' }
    ]
  },
  {
    name: '技术合作',
    partners: [
      { name: '阿里云', logo: '/partners/aliyun.png' },
      { name: '腾讯云', logo: '/partners/qcloud.png' },
      { name: '华为云', logo: '/partners/huawei.png' },
      { name: '百度云', logo: '/partners/baidu.png' }
    ]
  }
]

const certifications = [
  {
    icon: Lock,
    name: 'ISO27001认证',
    description: '信息安全管理体系国际标准认证',
    number: 'ISO27001:2013'
  },
  {
    icon: Lock,
    name: '等保三级',
    description: '国家信息安全等级保护三级认证',
    number: '等保备案号：***********-30001'
  },
  {
    icon: Star,
    name: '支付业务许可证',
    description: '中国人民银行颁发的支付业务许可证',
    number: '许可证编号：Z2021110000001'
  },
  {
    icon: Service,
    name: 'PCI DSS认证',
    description: '支付卡行业数据安全标准认证',
    number: 'PCI DSS Level 1'
  }
]

import { getAboutContactInfo } from '../config/contact.js'

const contactInfo = getAboutContactInfo().map(contact => ({
  type: contact.type,
  icon: contact.type === 'phone' ? Phone :
        contact.type === 'email' ? Message :
        contact.type === 'address' ? Location : Timer,
  label: contact.label,
  value: contact.value
}))

const submitForm = () => {
  console.log('提交表单:', contactForm.value)
  // 这里可以添加表单提交逻辑
}
</script>

<style scoped>
.about-page {
  flex: 1;
}

.page-hero {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: 120px 0 80px;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.company-intro {
  padding: 100px 0;
  background: white;
}

.intro-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: center;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 2rem;
}

.mission-text {
  font-size: 1.2rem;
  line-height: 1.8;
  color: var(--text-light);
  margin-bottom: 3rem;
}

.company-values {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.value-item {
  text-align: center;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 16px;
}

.value-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 12px;
  color: white;
  margin: 0 auto 1rem;
}

.value-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.8rem;
}

.value-desc {
  color: var(--text-light);
  line-height: 1.5;
}

.company-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.stat-item {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 16px;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-color);
}

.stat-label {
  color: var(--text-light);
  margin-top: 0.5rem;
}

.timeline-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.timeline {
  max-width: 800px;
  margin: 0 auto;
}

.timeline-item {
  display: flex;
  gap: 2rem;
  margin-bottom: 3rem;
}

.timeline-marker {
  position: relative;
  flex-shrink: 0;
}

.marker-dot {
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 0 0 4px var(--primary-color);
}

.marker-line {
  position: absolute;
  left: 50%;
  top: 20px;
  width: 2px;
  height: 60px;
  background: var(--border-color);
  transform: translateX(-50%);
}

.timeline-content {
  flex: 1;
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.timeline-date {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.timeline-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.timeline-desc {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.timeline-achievements {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color);
  font-size: 0.9rem;
}

.achievement-icon {
  color: var(--secondary-color);
}

.team-section {
  padding: 100px 0;
  background: white;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.team-member {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.member-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 1.5rem;
  background: #f8fafc;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.member-title {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 1rem;
}

.member-bio {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.member-experience {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.experience-item {
  background: #f1f5f9;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  color: var(--text-color);
}

.partners-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
}

.partners-categories {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.category-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 2rem;
  text-align: center;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.partner-item {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.partner-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.partner-logo {
  width: 100px;
  height: 100px;
  margin: 0 auto 1rem;
  background: #ffffff;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #f1f5f9;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.partner-logo:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: #e2e8f0;
}

.partner-logo img {
  max-width: 85%;
  max-height: 85%;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.partner-name {
  font-weight: 600;
  color: var(--text-color);
}

.certifications-section {
  padding: 100px 0;
  background: white;
}

.certifications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.cert-item {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.cert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 20px;
  color: white;
  margin: 0 auto 1.5rem;
}

.cert-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.cert-desc {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.cert-number {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 0.9rem;
}

.contact-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 12px;
  color: white;
}

.contact-label {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.3rem;
}

.contact-value {
  color: var(--text-light);
}

.contact-form {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.contact-form h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2.5rem;
  }

  .intro-content {
    grid-template-columns: 1fr;
  }

  .company-values {
    grid-template-columns: 1fr;
  }

  .company-stats {
    grid-template-columns: 1fr;
  }

  .timeline-item {
    flex-direction: column;
    gap: 1rem;
  }

  .team-grid {
    grid-template-columns: 1fr;
  }

  .contact-content {
    grid-template-columns: 1fr;
  }

  .partners-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .partner-logo {
    width: 80px;
    height: 80px;
  }

  .partner-item {
    padding: 1.5rem;
  }
}
</style>
