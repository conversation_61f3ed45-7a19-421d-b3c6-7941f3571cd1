<template>
  <div class="legal-page">
    <!-- 页面头部 -->
    <div class="page-hero">
      <div class="container">
        <h1 class="page-title">隐私政策</h1>
        <p class="page-subtitle">我们重视您的隐私，致力于保护您的个人信息安全</p>
        <div class="update-info">
          <el-icon><Calendar /></el-icon>
          <span>最后更新时间：{{ lastUpdated }}</span>
        </div>
      </div>
    </div>

    <!-- 政策内容 -->
    <div class="policy-content">
      <div class="container-narrow">
        <!-- 目录导航 -->
        <div class="table-of-contents">
          <h3>目录</h3>
          <ul class="toc-list">
            <li v-for="(section, index) in sections" :key="index">
              <a :href="`#section-${index}`" @click="scrollToSection(index)">
                {{ section.title }}
              </a>
            </li>
          </ul>
        </div>

        <!-- 政策条款 -->
        <div class="policy-sections">
          <div 
            v-for="(section, index) in sections" 
            :key="index"
            :id="`section-${index}`"
            class="policy-section"
          >
            <h2 class="section-title">{{ section.title }}</h2>
            <div class="section-content">
              <div v-for="(item, itemIndex) in section.content" :key="itemIndex">
                <h3 v-if="item.subtitle" class="subsection-title">{{ item.subtitle }}</h3>
                <p v-if="item.text" class="section-text">{{ item.text }}</p>
                <ul v-if="item.list" class="section-list">
                  <li v-for="(listItem, listIndex) in item.list" :key="listIndex">
                    {{ listItem }}
                  </li>
                </ul>
                <div v-if="item.table" class="section-table">
                  <el-table :data="item.table" style="width: 100%">
                    <el-table-column 
                      v-for="column in item.tableColumns" 
                      :key="column.prop"
                      :prop="column.prop" 
                      :label="column.label"
                      :width="column.width"
                    />
                  </el-table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 联系信息 -->
        <div class="contact-section">
          <div class="contact-card">
            <h3>如有疑问，请联系我们</h3>
            <div class="contact-methods">
              <div
                v-for="contact in contactMethods"
                :key="contact.type"
                class="contact-item"
              >
                <el-icon>
                  <component :is="contact.icon" />
                </el-icon>
                <div>
                  <strong>{{ contact.label }}</strong>
                  <p>{{ contact.value }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Calendar, Phone, Message, Location } from '@element-plus/icons-vue'
import { getPrivacyContactMethods } from '../config/contact.js'

const lastUpdated = ref('2024年6月17日')

const contactMethods = getPrivacyContactMethods().map(contact => ({
  ...contact,
  icon: contact.type === 'phone' ? 'Phone' :
        contact.type === 'email' ? 'Message' : 'Location'
}))

const sections = ref([
  {
    title: '1. 引言',
    content: [
      {
        text: 'ESoonPay（以下简称"我们"或"平台"）深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。'
      },
      {
        text: '本隐私政策适用于您通过ESoonPay平台使用我们的产品和服务。请您在使用我们的产品或服务前，仔细阅读并充分理解本隐私政策，特别是以粗体标识的条款。'
      }
    ]
  },
  {
    title: '2. 我们收集的信息',
    content: [
      {
        subtitle: '2.1 您主动提供的信息',
        list: [
          '注册信息：包括您的姓名、手机号码、邮箱地址、公司名称等',
          '身份验证信息：包括身份证号码、营业执照信息等',
          '银行账户信息：用于资金结算的银行账户相关信息',
          '联系信息：包括通讯地址、联系电话等'
        ]
      },
      {
        subtitle: '2.2 我们自动收集的信息',
        list: [
          '设备信息：包括设备型号、操作系统、设备标识符等',
          '日志信息：包括访问时间、访问页面、IP地址等',
          '位置信息：基于您的IP地址获取的大致地理位置',
          '交易信息：包括交易金额、交易时间、交易对象等'
        ]
      }
    ]
  },
  {
    title: '3. 信息使用目的',
    content: [
      {
        text: '我们收集和使用您的个人信息主要用于以下目的：'
      },
      {
        list: [
          '为您提供分账系统服务，包括账户开立、资金管理、交易处理等',
          '进行身份验证和风险控制，确保交易安全',
          '改进我们的产品和服务质量',
          '向您发送服务通知、安全提醒等重要信息',
          '遵守法律法规要求，配合监管部门工作',
          '处理您的咨询、投诉和建议'
        ]
      }
    ]
  },
  {
    title: '4. 信息共享与披露',
    content: [
      {
        text: '我们不会向第三方出售、出租或以其他方式披露您的个人信息，除非：'
      },
      {
        list: [
          '获得您的明确同意',
          '根据法律法规要求或监管部门要求',
          '为维护ESoonPay或其他用户的合法权益',
          '与我们的合作银行共享必要信息以提供服务',
          '在紧急情况下为保护用户或公众的人身安全'
        ]
      }
    ]
  },
  {
    title: '5. 信息安全保护',
    content: [
      {
        text: '我们采用行业标准的安全措施保护您的个人信息：'
      },
      {
        subtitle: '5.1 技术保护措施',
        list: [
          '数据加密：采用SSL/TLS加密传输，AES-256加密存储',
          '访问控制：实施严格的权限管理和身份认证',
          '安全监控：7×24小时安全监控和异常检测',
          '定期备份：建立完善的数据备份和恢复机制'
        ]
      },
      {
        subtitle: '5.2 管理保护措施',
        list: [
          '员工培训：定期进行信息安全培训',
          '权限管理：最小权限原则，定期审查权限',
          '安全审计：定期进行安全评估和渗透测试',
          '应急响应：建立完善的安全事件响应机制'
        ]
      }
    ]
  },
  {
    title: '6. 您的权利',
    content: [
      {
        text: '根据相关法律法规，您享有以下权利：'
      },
      {
        subtitle: '6.1 知情权',
        text: '您有权了解我们收集、使用您个人信息的情况。'
      },
      {
        subtitle: '6.2 访问权',
        text: '您有权访问我们持有的您的个人信息。'
      },
      {
        subtitle: '6.3 更正权',
        text: '如发现个人信息有误，您有权要求我们更正。'
      },
      {
        subtitle: '6.4 删除权',
        text: '在特定情况下，您有权要求我们删除您的个人信息。'
      },
      {
        subtitle: '6.5 撤回同意权',
        text: '您有权撤回对个人信息处理的同意，但不影响撤回前的合法处理。'
      }
    ]
  },
  {
    title: '7. Cookie和类似技术',
    content: [
      {
        text: '我们使用Cookie和类似技术来改善用户体验：'
      },
      {
        subtitle: '7.1 Cookie类型',
        list: [
          '必要Cookie：确保网站正常运行',
          '功能Cookie：记住您的偏好设置',
          '分析Cookie：帮助我们了解网站使用情况',
          '营销Cookie：用于个性化广告推送'
        ]
      },
      {
        subtitle: '7.2 Cookie管理',
        text: '您可以通过浏览器设置管理Cookie，但禁用某些Cookie可能影响网站功能。'
      }
    ]
  },
  {
    title: '8. 未成年人保护',
    content: [
      {
        text: '我们非常重视未成年人的个人信息保护：'
      },
      {
        list: [
          '我们的服务主要面向企业用户，不主动收集未成年人信息',
          '如无意中收集了未成年人信息，我们会立即删除',
          '未成年人使用我们的服务需要监护人同意',
          '我们建议监护人指导未成年人正确使用互联网服务'
        ]
      }
    ]
  },
  {
    title: '9. 跨境传输',
    content: [
      {
        text: '在某些情况下，我们可能需要将您的个人信息传输至境外：'
      },
      {
        subtitle: '9.1 传输情形',
        list: [
          '使用境外云服务进行数据备份',
          '与境外合作伙伴开展业务合作',
          '为您提供跨境支付服务',
          '遵守境外监管要求'
        ]
      },
      {
        subtitle: '9.2 保护措施',
        text: '我们会确保境外接收方提供充分的数据保护水平，并采取适当的安全措施。'
      }
    ]
  },
  {
    title: '10. 政策更新',
    content: [
      {
        text: '我们可能会不时更新本隐私政策：'
      },
      {
        list: [
          '重大变更会通过邮件、短信或网站公告通知您',
          '您可以随时查看最新版本的隐私政策',
          '继续使用我们的服务表示您接受更新后的政策',
          '如不同意更新内容，您可以停止使用我们的服务'
        ]
      }
    ]
  }
])

const scrollToSection = (index) => {
  const element = document.getElementById(`section-${index}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

onMounted(() => {
  // 页面加载完成后的初始化操作
})
</script>

<style scoped>
.legal-page {
  min-height: 100vh;
  background: #f8fafc;
}

.page-hero {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: 120px 0 80px;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.update-info {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
}

.policy-content {
  padding: 80px 0;
}

.container-narrow {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  align-items: start;
}

.table-of-contents {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 20px;
  z-index: 5;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

.table-of-contents h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

.toc-list {
  list-style: none;
}

.toc-list li {
  margin-bottom: 0.8rem;
}

.toc-list a {
  color: var(--text-color);
  text-decoration: none;
  padding: 0.5rem 0;
  display: block;
  border-left: 3px solid transparent;
  padding-left: 1rem;
  transition: all 0.3s ease;
}

.toc-list a:hover {
  color: var(--primary-color);
  border-left-color: var(--primary-color);
  background: rgba(30, 58, 138, 0.05);
}

.policy-sections {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 3rem;
}

.policy-section {
  padding: 3rem;
  border-bottom: 1px solid #e5e7eb;
}

.policy-section:last-child {
  border-bottom: none;
}

.section-title {
  color: var(--primary-color);
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--primary-color);
}

.subsection-title {
  color: var(--text-color);
  font-size: 1.3rem;
  font-weight: 600;
  margin: 2rem 0 1rem;
}

.section-text {
  color: var(--text-light);
  line-height: 1.8;
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.section-list {
  margin: 1.5rem 0;
  padding-left: 0;
  list-style: none;
}

.section-list li {
  position: relative;
  padding-left: 2rem;
  margin-bottom: 1rem;
  color: var(--text-light);
  line-height: 1.6;
}

.section-list li::before {
  content: '•';
  color: var(--primary-color);
  font-weight: bold;
  position: absolute;
  left: 0.5rem;
}

.section-table {
  margin: 2rem 0;
}

.contact-section {
  grid-column: 1 / -1;
  margin-top: 2rem;
}

.contact-card {
  background: white;
  padding: 3rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.contact-card h3 {
  color: var(--primary-color);
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

.contact-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  text-align: left;
}

.contact-item .el-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.contact-item strong {
  color: var(--text-color);
  display: block;
  margin-bottom: 0.3rem;
}

.contact-item p {
  color: var(--text-light);
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2.5rem;
  }

  .container-narrow {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0 1rem;
  }

  .table-of-contents {
    position: static;
    margin-bottom: 2rem;
    max-height: none;
  }

  .policy-section {
    padding: 2rem;
  }

  .contact-methods {
    grid-template-columns: 1fr;
  }

  .contact-card {
    padding: 2rem;
  }
}
</style>
