<template>
  <div class="scenarios-page">
    <!-- 页面头部 -->
    <section class="page-hero">
      <div class="container-wide">
        <div class="hero-content">
          <h1 class="page-title">应用场景</h1>
          <p class="page-subtitle">
            分账系统在不同电商业务场景中的实际应用，助力企业实现数字化转型
          </p>
        </div>
      </div>
    </section>

    <!-- 场景列表 -->
    <section class="scenarios-section">
      <div class="container-wide">
        <div class="scenarios-list">
          <div class="scenario-item" v-for="(scenario, index) in scenarios" :key="scenario.id">
            <div class="scenario-content" :class="{ 'reverse': index % 2 === 1 }">
              <div class="scenario-text">
                <div class="scenario-header">
                  <div class="scenario-icon">
                    <el-icon :size="40">
                      <component :is="scenario.icon" />
                    </el-icon>
                  </div>
                  <div class="scenario-meta">
                    <h3 class="scenario-title">{{ scenario.title }}</h3>
                    <p class="scenario-subtitle">{{ scenario.subtitle }}</p>
                  </div>
                </div>
                
                <div class="scenario-description">
                  <p>{{ scenario.description }}</p>
                </div>
                
                <div class="scenario-challenges">
                  <h4>传统方式面临的挑战</h4>
                  <ul class="challenge-list">
                    <li v-for="challenge in scenario.challenges" :key="challenge">
                      <el-icon class="challenge-icon"><Close /></el-icon>
                      {{ challenge }}
                    </li>
                  </ul>
                </div>
                
                <div class="scenario-solutions">
                  <h4>虚拟公户解决方案</h4>
                  <ul class="solution-list">
                    <li v-for="solution in scenario.solutions" :key="solution">
                      <el-icon class="solution-icon"><Check /></el-icon>
                      {{ solution }}
                    </li>
                  </ul>
                </div>
                
                <div class="scenario-benefits">
                  <h4>实施效果</h4>
                  <div class="benefits-grid">
                    <div class="benefit-item" v-for="benefit in scenario.benefits" :key="benefit.label">
                      <div class="benefit-value">{{ benefit.value }}</div>
                      <div class="benefit-label">{{ benefit.label }}</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="scenario-visual">
                <div class="visual-container">
                  <div class="flow-diagram">
                    <div class="flow-step" v-for="(step, stepIndex) in scenario.flowSteps" :key="stepIndex">
                      <div class="step-icon">
                        <el-icon>
                          <component :is="step.icon" />
                        </el-icon>
                      </div>
                      <div class="step-text">{{ step.text }}</div>
                      <div class="step-arrow" v-if="stepIndex < scenario.flowSteps.length - 1">
                        <el-icon><ArrowDown /></el-icon>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 成功案例统计 -->
    <section class="stats-section">
      <div class="container">
        <h2 class="section-title">应用成果</h2>
        <div class="stats-grid">
          <div class="stat-item" v-for="stat in applicationStats" :key="stat.label">
            <div class="stat-icon">
              <el-icon :size="32">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-desc">{{ stat.description }}</div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import {
  Shop,
  Box,
  Ship,
  Money,
  Close,
  Check,
  ArrowDown,
  ShoppingBag,
  Connection,
  Timer,
  TrendCharts,
  UserFilled
} from '@element-plus/icons-vue'

const scenarios = [
  {
    id: 'multi-store',
    icon: Shop,
    title: '多店铺管理',
    subtitle: '统一管理抖音小店、天猫店、自有商城的资金池',
    description: '适用于在多个电商平台开设店铺的商家，通过虚拟公户实现资金的统一管理和调配。',
    challenges: [
      '资金分散在多个平台，难以统一管理',
      '各平台提现规则不同，操作复杂',
      '资金调配不及时，影响运营效率',
      '财务对账工作量大，容易出错'
    ],
    solutions: [
      '自动归集各平台资金到虚拟公户',
      '统一的资金管理界面',
      'T+0实时提现，提升资金效率',
      '自动对账，减少人工错误'
    ],
    benefits: [
      { value: '90%', label: '管理效率提升' },
      { value: '70%', label: '对账时间减少' },
      { value: 'T+0', label: '资金到账时效' },
      { value: '95%', label: '错误率降低' }
    ],
    flowSteps: [
      { icon: ShoppingBag, text: '多平台收入' },
      { icon: Connection, text: '自动归集' },
      { icon: Timer, text: '实时到账' },
      { icon: TrendCharts, text: '统一管理' }
    ]
  },
  {
    id: 'supply-chain',
    icon: Box,
    title: '供应链结算',
    subtitle: '自动分账给供应商/物流商，缩短结算周期',
    description: '为有复杂供应链关系的电商企业提供自动化结算解决方案，提升供应链协作效率。',
    challenges: [
      '供应商结算周期长，影响合作关系',
      '手动结算容易出错，纠纷频发',
      '结算流程复杂，占用大量人力',
      '资金流转不透明，难以监控'
    ],
    solutions: [
      '智能分账规则，自动结算',
      '实时到账，提升供应商满意度',
      '透明的资金流转记录',
      '异常处理和风控机制'
    ],
    benefits: [
      { value: '80%', label: '结算周期缩短' },
      { value: '95%', label: '结算准确率' },
      { value: '60%', label: '人力成本节省' },
      { value: '100%', label: '流程透明度' }
    ],
    flowSteps: [
      { icon: Money, text: '订单收入' },
      { icon: TrendCharts, text: '智能分账' },
      { icon: Timer, text: '实时结算' },
      { icon: UserFilled, text: '供应商到账' }
    ]
  },
  {
    id: 'cross-border',
    icon: Ship,
    title: '跨境贸易',
    subtitle: '支持多币种结算，实时汇率转换',
    description: '为跨境电商提供多币种资金管理服务，降低汇率风险，提升资金使用效率。',
    challenges: [
      '多币种管理复杂，汇率风险大',
      '跨境结算手续费高',
      '资金到账时间长，影响周转',
      '合规要求严格，操作复杂'
    ],
    solutions: [
      '多币种虚拟账户支持',
      '智能汇率优化，降低成本',
      '快速跨境结算通道',
      '合规性自动检查'
    ],
    benefits: [
      { value: '45%', label: '汇率成本节省' },
      { value: '70%', label: '结算时间缩短' },
      { value: '30%', label: '手续费降低' },
      { value: '100%', label: '合规保障' }
    ],
    flowSteps: [
      { icon: Ship, text: '跨境收入' },
      { icon: Connection, text: '汇率转换' },
      { icon: Timer, text: '快速结算' },
      { icon: Money, text: '本币到账' }
    ]
  },
  {
    id: 'commission',
    icon: Money,
    title: '佣金发放',
    subtitle: '批量支付推广员佣金，避免私卡流水过大',
    description: '为有大量推广员或达人合作的电商企业提供批量佣金发放服务，合规高效。',
    challenges: [
      '推广员数量多，手动发放效率低',
      '私人账户流水过大，存在风险',
      '佣金计算复杂，容易出错',
      '缺乏透明的佣金记录'
    ],
    solutions: [
      '批量佣金发放功能',
      '合规的企业账户操作',
      '自动佣金计算和分发',
      '完整的佣金发放记录'
    ],
    benefits: [
      { value: '300%', label: '发放效率提升' },
      { value: '99.9%', label: '计算准确率' },
      { value: '0', label: '合规风险' },
      { value: '100%', label: '记录完整性' }
    ],
    flowSteps: [
      { icon: TrendCharts, text: '佣金计算' },
      { icon: UserFilled, text: '批量处理' },
      { icon: Timer, text: '实时发放' },
      { icon: Check, text: '到账确认' }
    ]
  }
]

const applicationStats = [
  {
    icon: Shop,
    value: '5,000+',
    label: '多店铺商家',
    description: '使用虚拟公户管理多平台资金'
  },
  {
    icon: Box,
    value: '3,000+',
    label: '供应链企业',
    description: '实现自动化供应商结算'
  },
  {
    icon: Ship,
    value: '1,200+',
    label: '跨境商家',
    description: '享受多币种结算服务'
  },
  {
    icon: Money,
    value: '800+',
    label: '直播机构',
    description: '使用批量佣金发放功能'
  }
]
</script>

<style scoped>
.scenarios-page {
  flex: 1;
}

.page-hero {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: 120px 0 80px;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.scenarios-section {
  padding: 100px 0;
  background: white;
}

.scenarios-list {
  display: flex;
  flex-direction: column;
  gap: 6rem;
}

.scenario-item {
  position: relative;
}

.scenario-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.scenario-content.reverse {
  direction: rtl;
}

.scenario-content.reverse > * {
  direction: ltr;
}

.scenario-header {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.scenario-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 20px;
  color: white;
  flex-shrink: 0;
}

.scenario-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.scenario-subtitle {
  color: var(--text-light);
  font-size: 1.1rem;
}

.scenario-description {
  margin-bottom: 2rem;
}

.scenario-description p {
  color: var(--text-light);
  line-height: 1.6;
  font-size: 1.1rem;
}

.scenario-challenges,
.scenario-solutions {
  margin-bottom: 2rem;
}

.scenario-challenges h4,
.scenario-solutions h4,
.scenario-benefits h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.challenge-list,
.solution-list {
  list-style: none;
}

.challenge-list li,
.solution-list li {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 0.8rem;
  color: var(--text-color);
}

.challenge-icon {
  color: #ef4444;
  font-size: 1rem;
}

.solution-icon {
  color: var(--secondary-color);
  font-size: 1rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.benefit-item {
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
}

.benefit-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.benefit-label {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 0.3rem;
}

.scenario-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.visual-container {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 20px;
  padding: 3rem;
  width: 100%;
  max-width: 400px;
}

.flow-diagram {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  align-items: center;
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 50%;
  color: white;
  margin-bottom: 1rem;
}

.step-text {
  font-weight: 600;
  color: var(--text-color);
  text-align: center;
}

.step-arrow {
  position: absolute;
  bottom: -1.5rem;
  color: var(--text-light);
  font-size: 1.5rem;
}

.stats-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.stat-item {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 20px;
  color: white;
  margin: 0 auto 1.5rem;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.8rem;
}

.stat-desc {
  color: var(--text-light);
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2.5rem;
  }

  .scenario-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .scenario-content.reverse {
    direction: ltr;
  }

  .scenario-header {
    flex-direction: column;
    text-align: center;
  }

  .benefits-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
