<template>
  <div class="solutions-page">
    <!-- 页面头部 -->
    <section class="page-hero">
      <div class="container-wide">
        <div class="hero-content">
          <h1 class="page-title">解决方案</h1>
          <p class="page-subtitle">
            基于银行级技术架构，为不同规模的电商企业提供定制化的分账系统解决方案
          </p>
        </div>
      </div>
    </section>

    <!-- 解决方案列表 -->
    <section class="solutions-section">
      <div class="container-wide">
        <div class="solutions-grid">
          <div class="solution-card" v-for="solution in solutions" :key="solution.id">
            <div class="card-header">
              <div class="solution-icon">
                <el-icon :size="48">
                  <component :is="solution.icon" />
                </el-icon>
              </div>
              <div class="solution-meta">
                <h3 class="solution-title">{{ solution.title }}</h3>
                <p class="solution-desc">{{ solution.description }}</p>
              </div>
            </div>
            
            <div class="solution-features">
              <h4>核心功能</h4>
              <ul class="feature-list">
                <li v-for="feature in solution.features" :key="feature">
                  <el-icon class="feature-icon"><Check /></el-icon>
                  {{ feature }}
                </li>
              </ul>
            </div>
            
            <div class="solution-benefits">
              <h4>业务价值</h4>
              <div class="benefits-grid">
                <div class="benefit-item" v-for="benefit in solution.benefits" :key="benefit.label">
                  <div class="benefit-value">{{ benefit.value }}</div>
                  <div class="benefit-label">{{ benefit.label }}</div>
                </div>
              </div>
            </div>
            
            <div class="solution-action">
              <el-button type="primary" @click="learnMore(solution.id)">
                了解详情
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 技术架构 -->
    <section class="architecture-section">
      <div class="container">
        <h2 class="section-title">技术架构</h2>
        <div class="architecture-diagram">
          <!-- 这里可以添加技术架构图 -->
          <div class="arch-layers">
            <div class="arch-layer" v-for="layer in architectureLayers" :key="layer.name">
              <h4>{{ layer.name }}</h4>
              <div class="layer-components">
                <span v-for="component in layer.components" :key="component" class="component">
                  {{ component }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- API文档 -->
    <section class="api-section">
      <div class="container">
        <h2 class="section-title">API接口</h2>
        <div class="api-grid">
          <div class="api-card" v-for="api in apiList" :key="api.name">
            <h4>{{ api.name }}</h4>
            <p>{{ api.description }}</p>
            <div class="api-methods">
              <span v-for="method in api.methods" :key="method" class="method-tag">
                {{ method }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import {
  Connection,
  Timer,
  TrendCharts,
  Lock,
  Check
} from '@element-plus/icons-vue'

const solutions = [
  {
    id: 'aggregation',
    icon: Connection,
    title: '多平台资金聚合',
    description: '统一管理抖音、天猫、京东等20+电商平台的资金收入',
    features: [
      '支持20+主流电商平台',
      'API自动对接，0代码接入',
      '实时数据同步',
      '统一资金视图',
      '多维度数据分析'
    ],
    benefits: [
      { value: '20+', label: '支持平台' },
      { value: '5分钟', label: '接入时间' },
      { value: '99.9%', label: '数据准确率' },
      { value: '90%', label: '管理效率提升' }
    ]
  },
  {
    id: 'realtime',
    icon: Timer,
    title: 'T+0实时到账',
    description: '突破传统银行限制，实现资金实时到账，提升资金使用效率',
    features: [
      '7×24小时服务',
      '无限制提现额度',
      '秒级到账速度',
      '节假日正常服务',
      '多种提现方式'
    ],
    benefits: [
      { value: 'T+0', label: '到账时效' },
      { value: '3秒', label: '最快到账' },
      { value: '无限制', label: '提现额度' },
      { value: '180%', label: '资金效率提升' }
    ]
  },
  {
    id: 'distribution',
    icon: TrendCharts,
    title: '智能分账系统',
    description: '基于预设规则自动分账，支持供应商、员工、合作伙伴批量结算',
    features: [
      '灵活分账规则配置',
      '批量处理能力',
      '实时状态监控',
      '自动对账功能',
      '异常处理机制'
    ],
    benefits: [
      { value: '1000+', label: '并发处理' },
      { value: '毫秒级', label: '处理速度' },
      { value: '100%', label: '准确率' },
      { value: '300%', label: '效率提升' }
    ]
  },
  {
    id: 'security',
    icon: Lock,
    title: '银行级安全',
    description: '央行监管的银行级安全保障，多重安全措施，资金安全无忧',
    features: [
      '央行监管合规',
      '资金完全隔离',
      '多重身份验证',
      '实时风控监控',
      '数据加密传输'
    ],
    benefits: [
      { value: '银行级', label: '安全等级' },
      { value: '100%', label: '资金隔离' },
      { value: '0', label: '安全事故' },
      { value: '24/7', label: '监控保护' }
    ]
  }
]

const architectureLayers = [
  {
    name: '应用层',
    components: ['Web管理台', '移动APP', 'API网关', '数据看板']
  },
  {
    name: '业务层',
    components: ['资金聚合', '智能分账', '风控引擎', '对账系统']
  },
  {
    name: '服务层',
    components: ['微服务架构', '消息队列', '缓存系统', '监控告警']
  },
  {
    name: '数据层',
    components: ['银行核心', '数据库集群', '备份系统', '加密存储']
  }
]

const apiList = [
  {
    name: '账户管理API',
    description: '虚拟账户的创建、查询、管理等操作',
    methods: ['GET', 'POST', 'PUT']
  },
  {
    name: '资金操作API',
    description: '充值、提现、转账等资金操作接口',
    methods: ['POST', 'GET']
  },
  {
    name: '分账管理API',
    description: '分账规则配置、执行、查询等功能',
    methods: ['POST', 'GET', 'PUT']
  },
  {
    name: '数据查询API',
    description: '交易记录、账户余额、统计数据查询',
    methods: ['GET']
  }
]

const learnMore = (solutionId) => {
  console.log('了解更多:', solutionId)
}
</script>

<style scoped>
.solutions-page {
  flex: 1;
}

.page-hero {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: 120px 0 80px;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.solutions-section {
  padding: 100px 0;
  background: white;
}

.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.solution-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.solution-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.solution-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 20px;
  color: white;
  flex-shrink: 0;
}

.solution-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 0.8rem;
}

.solution-desc {
  color: var(--text-light);
  line-height: 1.6;
}

.solution-features,
.solution-benefits {
  margin-bottom: 2rem;
}

.solution-features h4,
.solution-benefits h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.feature-list {
  list-style: none;
}

.feature-list li {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 0.8rem;
  color: var(--text-color);
}

.feature-icon {
  color: var(--secondary-color);
  font-size: 1rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.benefit-item {
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
}

.benefit-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.benefit-label {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 0.3rem;
}

.solution-action {
  text-align: center;
}

.architecture-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 3rem;
}

.arch-layers {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.arch-layer {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.arch-layer h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.layer-components {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.component {
  background: #f1f5f9;
  padding: 0.8rem;
  border-radius: 8px;
  color: var(--text-color);
  font-size: 0.9rem;
}

.api-section {
  padding: 100px 0;
  background: white;
}

.api-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.api-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.api-card h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.api-card p {
  color: var(--text-light);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.api-methods {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.method-tag {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2.5rem;
  }

  .solutions-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    text-align: center;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .arch-layers {
    grid-template-columns: 1fr;
  }

  .api-grid {
    grid-template-columns: 1fr;
  }
}
</style>
