<template>
  <div class="legal-page">
    <!-- 页面头部 -->
    <div class="page-hero">
      <div class="container">
        <h1 class="page-title">服务条款</h1>
        <p class="page-subtitle">请仔细阅读以下条款，使用我们的服务即表示您同意这些条款</p>
        <div class="update-info">
          <el-icon><Calendar /></el-icon>
          <span>最后更新时间：{{ lastUpdated }}</span>
        </div>
      </div>
    </div>

    <!-- 条款内容 -->
    <div class="terms-content">
      <div class="container-narrow">
        <!-- 重要提示 -->
        <div class="important-notice">
          <div class="notice-header">
            <el-icon><Warning /></el-icon>
            <h3>重要提示</h3>
          </div>
          <p>在使用ESoonPay服务之前，请您务必仔细阅读并充分理解本服务条款的全部内容。您的注册、登录、使用等行为将视为对本协议的接受，并同意接受本协议各项条款的约束。</p>
        </div>

        <!-- 内容布局 -->
        <div class="content-layout">
          <!-- 目录导航 -->
          <div class="table-of-contents">
            <h3>条款目录</h3>
            <ul class="toc-list">
              <li v-for="(section, index) in sections" :key="index">
                <a :href="`#section-${index}`" @click="scrollToSection(index)">
                  {{ section.title }}
                </a>
              </li>
            </ul>
          </div>

          <!-- 主要内容 -->
          <div class="main-content">
            <!-- 条款内容 -->
            <div class="terms-sections">
              <div
                v-for="(section, index) in sections"
                :key="index"
                :id="`section-${index}`"
                class="terms-section"
              >
                <h2 class="section-title">{{ section.title }}</h2>
                <div class="section-content">
                  <div v-for="(item, itemIndex) in section.content" :key="itemIndex">
                    <h3 v-if="item.subtitle" class="subsection-title">{{ item.subtitle }}</h3>
                    <p v-if="item.text" class="section-text">{{ item.text }}</p>
                    <ul v-if="item.list" class="section-list">
                      <li v-for="(listItem, listIndex) in item.list" :key="listIndex">
                        {{ listItem }}
                      </li>
                    </ul>
                    <div v-if="item.highlight" class="highlight-box">
                      <el-icon><InfoFilled /></el-icon>
                      <p>{{ item.highlight }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 同意确认 -->
            <div class="agreement-section">
              <div class="agreement-card">
                <h3>条款确认</h3>
                <p>通过使用ESoonPay的服务，您确认已阅读、理解并同意受本服务条款的约束。如果您不同意本条款的任何部分，请不要使用我们的服务。</p>
                <div class="agreement-actions">
                  <el-button type="primary" size="large" @click="goToRegister">
                    我已阅读并同意条款
                  </el-button>
                  <el-button size="large" @click="goBack">
                    返回上一页
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Calendar, Warning, InfoFilled } from '@element-plus/icons-vue'
import { CONTACT_INFO } from '../config/contact.js'

const router = useRouter()
const lastUpdated = ref('2024年6月17日')

const sections = ref([
  {
    title: '1. 服务说明',
    content: [
      {
        text: 'ESoonPay是一个专业的电商企业分账系统服务平台，为电商企业提供资金管理、多平台聚合收款、智能分账等金融科技服务。'
      },
      {
        subtitle: '1.1 服务范围',
        list: [
          '分账系统开立与管理',
          '多平台资金聚合收款',
          '智能分账与结算',
          '资金流水管理',
          '供应链金融服务',
          '风险控制与合规管理'
        ]
      },
      {
        highlight: '重要提示：我们的服务仅面向合法经营的企业用户，个人用户暂不支持注册使用。'
      }
    ]
  },
  {
    title: '2. 用户注册与账户',
    content: [
      {
        subtitle: '2.1 注册条件',
        list: [
          '具有完全民事行为能力的企业法人或其授权代表',
          '提供真实、准确、完整的企业信息和联系方式',
          '具备合法的经营资质和相关许可证件',
          '同意遵守本服务条款及相关法律法规'
        ]
      },
      {
        subtitle: '2.2 账户安全',
        text: '您有责任维护账户和密码的安全性，对于因您的疏忽导致的账户安全问题，您将承担相应责任。建议您定期更换密码，不要将账户信息透露给他人。'
      },
      {
        highlight: '如发现账户异常或安全问题，请立即联系我们的客服团队。'
      }
    ]
  },
  {
    title: '3. 服务费用',
    content: [
      {
        text: '我们的服务费用将根据您使用的具体服务类型和交易量进行收取。具体费用标准请参考我们的价格页面或联系客服咨询。'
      },
      {
        subtitle: '3.1 费用类型',
        list: [
          '账户开立费：一次性收取',
          '交易手续费：按交易金额比例收取',
          '提现费用：根据提现金额和银行政策收取',
          '增值服务费：根据具体服务内容收取'
        ]
      },
      {
        subtitle: '3.2 费用调整',
        text: '我们保留调整服务费用的权利，任何费用调整将提前30天通过邮件、短信或平台公告的方式通知您。'
      }
    ]
  },
  {
    title: '4. 用户义务与责任',
    content: [
      {
        subtitle: '4.1 合规使用',
        list: [
          '遵守国家法律法规和相关金融监管要求',
          '不得利用我们的服务进行洗钱、诈骗等违法活动',
          '确保交易资金来源合法',
          '配合我们进行必要的身份验证和风险控制'
        ]
      },
      {
        subtitle: '4.2 信息准确性',
        text: '您承诺提供的所有信息均真实、准确、完整，如信息发生变更，应及时更新。因信息不准确导致的损失由您承担。'
      }
    ]
  },
  {
    title: '5. 平台权利与义务',
    content: [
      {
        subtitle: '5.1 服务提供',
        text: '我们承诺为您提供稳定、安全的服务，但不保证服务不会中断或完全无错误。我们会尽力维护系统稳定性，并在出现问题时及时修复。'
      },
      {
        subtitle: '5.2 风险控制',
        list: [
          '建立完善的风险控制体系',
          '对异常交易进行监控和处理',
          '配合监管部门的检查和调查',
          '保护用户资金和信息安全'
        ]
      }
    ]
  },
  {
    title: '6. 资金安全保障',
    content: [
      {
        subtitle: '6.1 资金存管',
        text: '用户资金存管于合作银行的实体账户中，平台不直接接触用户资金，确保资金安全。'
      },
      {
        subtitle: '6.2 合作银行',
        list: [
          '百信银行：提供主要资金存管服务',
          '苏商银行：提供结算通道服务',
          '蓝海银行：提供跨境支付服务',
          '新网银行：提供企业账户服务'
        ]
      },
      {
        subtitle: '6.3 监管合规',
        text: '所有资金操作均受中国人民银行等监管机构监督，严格遵守反洗钱等法规要求。'
      }
    ]
  },
  {
    title: '7. 服务中断与维护',
    content: [
      {
        subtitle: '7.1 计划性维护',
        text: '我们会提前通知计划性系统维护，尽量安排在业务低峰期进行，减少对您业务的影响。'
      },
      {
        subtitle: '7.2 紧急维护',
        text: '如遇紧急情况需要立即维护，我们会尽快恢复服务并及时通知用户。'
      },
      {
        subtitle: '7.3 补偿机制',
        list: [
          '因我方原因导致的服务中断，我们将提供相应补偿',
          '补偿方式包括延长服务期限、减免费用等',
          '具体补偿标准根据影响程度确定'
        ]
      }
    ]
  },
  {
    title: '8. 知识产权',
    content: [
      {
        subtitle: '8.1 平台知识产权',
        text: 'ESoonPay平台的所有技术、软件、商标、版权等知识产权均归我们所有。'
      },
      {
        subtitle: '8.2 用户内容',
        text: '您上传或提供的内容，版权归您所有，但您授权我们为提供服务而使用这些内容。'
      },
      {
        subtitle: '8.3 侵权处理',
        text: '如发现侵犯知识产权的行为，我们将依法处理，包括删除侵权内容、终止服务等。'
      }
    ]
  },
  {
    title: '9. 免责声明',
    content: [
      {
        subtitle: '9.1 不可抗力',
        text: '因自然灾害、政府行为、网络攻击等不可抗力因素导致的服务中断或损失，我们不承担责任。'
      },
      {
        subtitle: '9.2 第三方服务',
        text: '对于第三方提供的服务（如银行服务、网络服务等）出现的问题，我们不承担直接责任。'
      },
      {
        subtitle: '9.3 用户行为',
        text: '用户违法违规行为导致的后果，由用户自行承担，我们不承担连带责任。'
      }
    ]
  },
  {
    title: '10. 争议解决',
    content: [
      {
        subtitle: '10.1 协商解决',
        text: '如发生争议，双方应首先通过友好协商解决。'
      },
      {
        subtitle: '10.2 调解程序',
        text: '协商不成的，可申请相关行业调解组织进行调解。'
      },
      {
        subtitle: '10.3 法律途径',
        text: '调解不成的，任何一方均可向北京市朝阳区人民法院提起诉讼。'
      },
      {
        highlight: '本协议适用中华人民共和国法律，如有冲突以中国法律为准。'
      }
    ]
  },
  {
    title: '11. 条款变更',
    content: [
      {
        text: '我们保留修改本服务条款的权利：'
      },
      {
        list: [
          '重大变更将提前30天通过邮件、短信或平台公告通知',
          '您可以选择接受新条款或终止使用服务',
          '继续使用服务视为接受修改后的条款',
          '您有权在条款变更前导出个人数据'
        ]
      }
    ]
  }
])

const scrollToSection = (index) => {
  const element = document.getElementById(`section-${index}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const goToRegister = () => {
  // 跳转到注册页面
  try {
    import('../utils/deviceDetect.js').then(({ redirectToRegister }) => {
      const registerUrl = redirectToRegister()
      window.open(registerUrl, '_blank')
    })
  } catch (error) {
    console.error('跳转注册页面失败:', error)
  }
}

const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  // 页面加载完成后的初始化操作
})
</script>

<style scoped>
.legal-page {
  min-height: 100vh;
  background: #f8fafc;
}

.page-hero {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: 120px 0 80px;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.update-info {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
}

.terms-content {
  padding: 80px 0;
}

.container-narrow {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.important-notice {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 2px solid #f59e0b;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 3rem;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  align-items: start;
}

.notice-header {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.notice-header .el-icon {
  font-size: 1.5rem;
  color: #f59e0b;
}

.notice-header h3 {
  color: #92400e;
  margin: 0;
}

.important-notice p {
  color: #92400e;
  line-height: 1.6;
  margin: 0;
}

.table-of-contents {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 20px;
  z-index: 5;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

.table-of-contents h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

.toc-list {
  list-style: none;
}

.toc-list li {
  margin-bottom: 0.8rem;
}

.toc-list a {
  color: var(--text-color);
  text-decoration: none;
  padding: 0.5rem 0;
  display: block;
  border-left: 3px solid transparent;
  padding-left: 1rem;
  transition: all 0.3s ease;
}

.toc-list a:hover {
  color: var(--primary-color);
  border-left-color: var(--primary-color);
  background: rgba(30, 58, 138, 0.05);
}

.terms-sections {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 3rem;
}

.terms-section {
  padding: 3rem;
  border-bottom: 1px solid #e5e7eb;
}

.terms-section:last-child {
  border-bottom: none;
}

.section-title {
  color: var(--primary-color);
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--primary-color);
}

.subsection-title {
  color: var(--text-color);
  font-size: 1.3rem;
  font-weight: 600;
  margin: 2rem 0 1rem;
}

.section-text {
  color: var(--text-light);
  line-height: 1.8;
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.section-list {
  margin: 1.5rem 0;
  padding-left: 0;
  list-style: none;
}

.section-list li {
  position: relative;
  padding-left: 2rem;
  margin-bottom: 1rem;
  color: var(--text-light);
  line-height: 1.6;
}

.section-list li::before {
  content: '•';
  color: var(--primary-color);
  font-weight: bold;
  position: absolute;
  left: 0.5rem;
}

.highlight-box {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  border: 1px solid var(--primary-color);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 2rem 0;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.highlight-box .el-icon {
  color: var(--primary-color);
  font-size: 1.2rem;
  margin-top: 0.2rem;
}

.highlight-box p {
  color: var(--primary-color);
  margin: 0;
  font-weight: 500;
}

.agreement-section {
  margin-top: 2rem;
}

.agreement-card {
  background: white;
  padding: 3rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.agreement-card h3 {
  color: var(--primary-color);
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

.agreement-card p {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.agreement-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2.5rem;
  }

  .container-narrow {
    padding: 0 1rem;
  }

  .content-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .table-of-contents {
    position: static;
    margin-bottom: 2rem;
    max-height: none;
  }

  .terms-section {
    padding: 2rem;
  }

  .agreement-actions {
    flex-direction: column;
    align-items: center;
  }

  .agreement-card {
    padding: 2rem;
  }
}
</style>
